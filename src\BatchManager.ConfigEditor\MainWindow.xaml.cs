﻿using BatchManager.ConfigEditor.ViewModels;
using System.Windows;

namespace BatchManager.ConfigEditor;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        Loaded += Window_Loaded;
    }

    private void MenuItem_Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private async void Window_Loaded(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainViewModel viewModel)
        {
            // コマンドライン引数を取得
            var args = Environment.GetCommandLineArgs();
            string? configFilePath = args.Length > 1 ? args[1] : null;
            
            await viewModel.InitializeAsync(configFilePath);
        }
    }
}
