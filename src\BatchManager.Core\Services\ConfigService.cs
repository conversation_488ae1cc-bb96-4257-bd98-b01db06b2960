using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using System.Xml.Serialization;

namespace BatchManager.Core.Services;

/// <summary>
/// 設定ファイルの読み書きを行うサービス
/// </summary>
public class ConfigService : IConfigService
{
    private readonly ILogService _logService;
    private readonly IConfiguration? _configuration;

    public ConfigService(ILogService logService, IConfiguration? configuration = null)
    {
        _logService = logService;
        _configuration = configuration;
    }

    /// <summary>
    /// BatchManagerNet.configからジョブ設定を読み込む
    /// </summary>
    public async Task<ComListArray> LoadJobConfigAsync(string configFilePath)
    {
        try
        {
            _logService.Information($"ジョブ設定ファイルを読み込んでいます: {configFilePath}");

            if (!File.Exists(configFilePath))
            {
                _logService.Error($"設定ファイルが見つかりません: {configFilePath}");
                throw new FileNotFoundException("設定ファイルが見つかりません", configFilePath);
            }

            var serializer = new XmlSerializer(typeof(ComListArray));
            using var fileStream = new FileStream(configFilePath, FileMode.Open, FileAccess.Read);
            var config = (ComListArray?)serializer.Deserialize(fileStream);

            if (config == null || config.ComLists == null)
            {
                _logService.Warning("設定ファイルは空です");
                return new ComListArray { ComLists = Array.Empty<ComList>() };
            }

            _logService.Information($"ジョブ設定を読み込みました: {config.ComLists.Length}件");
            return config;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, $"設定ファイルの読み込みに失敗しました: {configFilePath}");
            throw;
        }
    }

    /// <summary>
    /// BatchManagerNet.configにジョブ設定を保存する
    /// </summary>
    public async Task SaveJobConfigAsync(string configFilePath, ComListArray config)
    {
        try
        {
            _logService.Information($"ジョブ設定ファイルを保存しています: {configFilePath}");

            // バックアップの作成
            if (File.Exists(configFilePath))
            {
                var backupPath = $"{configFilePath}.backup";
                File.Copy(configFilePath, backupPath, true);
                _logService.Debug($"設定ファイルのバックアップを作成しました: {backupPath}");
            }

            var serializer = new XmlSerializer(typeof(ComListArray));
            using var fileStream = new FileStream(configFilePath, FileMode.Create, FileAccess.Write);
            serializer.Serialize(fileStream, config);

            _logService.Information($"ジョブ設定を保存しました: {config.ComLists?.Length ?? 0}件");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, $"設定ファイルの保存に失敗しました: {configFilePath}");
            throw;
        }
    }

    /// <summary>
    /// BatchManagerNet.exe.configからアプリケーション設定を読み込む
    /// </summary>
    public AppSettings LoadAppSettings()
    {
        try
        {
            _logService.Information("アプリケーション設定を読み込んでいます");

            var settings = new AppSettings
            {
                // 基本設定
                InstanceName = GetAppSetting("InstanceName", "DefaultInstance"),
                CustomerName = GetAppSetting("CustomerName", ""),
                BatchDescription = GetAppSetting("BatchDescription", ""),
                ConfigFilePath = GetAppSetting("ConfigFilePath", "BatchManagerNet.config"),
                LogFilePath = GetAppSetting("LogFilePath", "BatchManagerNet.log"),

                // 新しいメール設定
                EmailEnabled = GetBoolSetting("EmailEnabled", true),
                EmailFrom = GetAppSetting("EmailFrom", ""),
                EmailTo = GetAppSetting("EmailTo", ""),
                SmtpHost = GetAppSetting("SmtpHost", ""),
                SmtpPort = GetIntSetting("SmtpPort", 587),
                SmtpEnableSsl = GetBoolSetting("SmtpEnableSsl", true),
                SmtpUsername = GetAppSetting("SmtpUsername", ""),
                SmtpPassword = GetAppSetting("SmtpPassword", ""),
                EmailSubjectPrefix = GetAppSetting("EmailSubjectPrefix", "[BatchManagerNet]"),

                // ログローテーション設定
                LogRotationEnabled = GetBoolSetting("LogRotationEnabled", false),
                LogRetentionDays = GetIntSetting("LogRetentionDays", 30),
                LogArchiveFolder = GetAppSetting("LogArchiveFolder", "Archive"),
                LogRotationTime = GetTimeSpanSetting("LogRotationTime", TimeSpan.Zero),

                // データベース連携設定
                DatabaseEnabled = GetBoolSetting("DatabaseEnabled", false),
                DatabaseType = GetAppSetting("DatabaseType", "PostgreSQL"),
                DbConnectionString = GetAppSetting("DbConnectionString", ""),

                // DB転送設定
                DbUploadMode = GetAppSetting("DbUploadMode", "AfterCompletion"),
                DbUploadAsync = GetBoolSetting("DbUploadAsync", true),
                DbUploadFailAction = GetAppSetting("DbUploadFailAction", "SaveAndContinue"),
                DbUploadRetryCount = GetIntSetting("DbUploadRetryCount", 3),
                DbUploadRetryInterval = GetIntSetting("DbUploadRetryInterval", 5),
                DbUploadTempFolder = GetAppSetting("DbUploadTempFolder", "UploadQueue"),
                DbUploadTimeout = GetIntSetting("DbUploadTimeout", 30),

                // ログ設定
                LogFormat = GetAppSetting("LogFormat", "JSON"),
                LogLevel = GetAppSetting("LogLevel", "Information")
            };

            // 旧メール設定（後方互換性のため）
            AssignLegacyEmailSettings(settings);

            _logService.Information($"アプリケーション設定を読み込みました: {settings.InstanceName}");
            _logService.Information($"メール設定: EmailEnabled={settings.EmailEnabled}, SmtpHost='{settings.SmtpHost}', EmailFrom='{settings.EmailFrom}', EmailTo='{settings.EmailTo}'");
            return settings;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "アプリケーション設定の読み込みに失敗しました");
            throw;
        }
    }

    /// <summary>
    /// 設定ファイルのバリデーションを行う
    /// </summary>
    public async Task<List<string>> ValidateJobConfigAsync(string configFilePath)
    {
        var errors = new List<string>();

        try
        {
            // 設定ファイルを読み込む
            var config = await LoadJobConfigAsync(configFilePath);

            if (config == null || config.ComLists == null || config.ComLists.Length == 0)
            {
                errors.Add("ジョブ設定が空です");
                return errors;
            }

            for (int i = 0; i < config.ComLists.Length; i++)
            {
                var job = config.ComLists[i];
                var jobPrefix = $"ジョブ {i + 1}";

                // コマンドラインの検証
                if (string.IsNullOrWhiteSpace(job.ComLine))
                {
                    errors.Add($"{jobPrefix}: コマンドラインが指定されていません");
                }
                else if (!File.Exists(job.ComLine))
                {
                    errors.Add($"{jobPrefix}: コマンドラインのファイルが存在しません: {job.ComLine}");
                }

                // タイムアウト設定の検証
                if (job.LimitTime <= 0)
                {
                    errors.Add($"{jobPrefix}: タイムアウト時間が不正です: {job.LimitTime}");
                }

                // 終了フラグの検証
                if (job.EndFlg < 1 || job.EndFlg > 4)
                {
                    errors.Add($"{jobPrefix}: 終了フラグが不正です: {job.EndFlg}");
                }

                // タイプの検証
                if (job.Type < 1 || job.Type > 2)
                {
                    errors.Add($"{jobPrefix}: タイプが不正です: {job.Type}");
                }
            }

            if (errors.Count == 0)
            {
                _logService.Information("設定ファイルのバリデーションに成功しました");
            }
            else
            {
                _logService.Warning($"設定ファイルのバリデーションに{errors.Count}個のエラーがあります");
            }
        }
        catch (Exception ex)
        {
            errors.Add($"設定ファイルの読み込みエラー: {ex.Message}");
            _logService.Error(ex, "設定ファイルのバリデーション中にエラーが発生しました");
        }

        return errors;
    }

    /// <summary>
    /// App.configから設定値を取得する
    /// </summary>
    private string GetAppSetting(string key, string defaultValue)
    {
        try
        {
            string? value = null;
            string? jsonValue = null;
            string? appConfigValue = null;

            if (_configuration != null)
            {
                jsonValue = _configuration[$"AppSettings:{key}"];
            }

            if (!string.IsNullOrEmpty(jsonValue))
            {
                jsonValue = jsonValue.Trim();
                value = jsonValue;
            }
            else
            {
                appConfigValue = System.Configuration.ConfigurationManager.AppSettings[key];
                if (!string.IsNullOrEmpty(appConfigValue))
                {
                    appConfigValue = appConfigValue.Trim();
                }

                value = appConfigValue;
            }

            if (!string.IsNullOrEmpty(value))
            {
                value = value.Trim();
            }

            var result = string.IsNullOrEmpty(value) ? defaultValue : value;

            _logService.Information(
                $"設定値解決: {key} Json='{MaskSensitiveValue(key, jsonValue)}' AppConfig='{MaskSensitiveValue(key, appConfigValue)}' Result='{MaskSensitiveValue(key, result)}'");

            return result;
        }
        catch
        {
            return defaultValue;
        }
    }

    private bool GetBoolSetting(string key, bool defaultValue, bool supportNumeric = false)
    {
        var value = GetAppSetting(key, defaultValue ? "true" : "false");
        if (supportNumeric)
        {
            if (value == "1") return true;
            if (value == "0") return false;
        }

        if (bool.TryParse(value, out var result))
        {
            return result;
        }

        return defaultValue;
    }

    private int GetIntSetting(string key, int defaultValue)
    {
        var value = GetAppSetting(key, defaultValue.ToString(CultureInfo.InvariantCulture));
        if (int.TryParse(value, out var result))
        {
            return result;
        }
        return defaultValue;
    }

    private TimeSpan GetTimeSpanSetting(string key, TimeSpan defaultValue)
    {
        var value = GetAppSetting(key, defaultValue.ToString());
        if (TimeSpan.TryParse(value, out var result))
        {
            return result;
        }
        return defaultValue;
    }

    private void AssignLegacyEmailSettings(AppSettings settings)
    {
        // FromMailAddress / ToMailAddress
        if (string.IsNullOrWhiteSpace(settings.FromMailAddress))
        {
            var legacyFrom = System.Configuration.ConfigurationManager.AppSettings[nameof(settings.FromMailAddress)];
            if (!string.IsNullOrWhiteSpace(legacyFrom))
            {
                settings.FromMailAddress = legacyFrom;
            }
        }

        if (string.IsNullOrWhiteSpace(settings.ToMailAddress))
        {
            var legacyTo = System.Configuration.ConfigurationManager.AppSettings[nameof(settings.ToMailAddress)];
            if (!string.IsNullOrWhiteSpace(legacyTo))
            {
                settings.ToMailAddress = legacyTo;
            }
        }

        // SMTP Host/User/Pass
        if (string.IsNullOrWhiteSpace(settings.SmtpHost))
        {
            var legacyClient = System.Configuration.ConfigurationManager.AppSettings[nameof(settings.SmtpClient)];
            if (!string.IsNullOrWhiteSpace(legacyClient))
            {
                settings.SmtpHost = legacyClient;
            }
        }

        if (string.IsNullOrWhiteSpace(settings.SmtpUsername))
        {
            var legacyUser = System.Configuration.ConfigurationManager.AppSettings[nameof(settings.User)];
            if (!string.IsNullOrWhiteSpace(legacyUser))
            {
                settings.SmtpUsername = legacyUser;
            }
        }

        if (string.IsNullOrWhiteSpace(settings.SmtpPassword))
        {
            var legacyPass = System.Configuration.ConfigurationManager.AppSettings[nameof(settings.Pass)];
            if (!string.IsNullOrWhiteSpace(legacyPass))
            {
                settings.SmtpPassword = legacyPass;
            }
        }

        // EnableSsl / Port
        settings.SmtpEnableSsl = settings.SmtpEnableSsl || GetBoolSetting(nameof(AppSettings.EnableSsl), true, supportNumeric: true);
        if (settings.SmtpPort <= 0)
        {
            settings.SmtpPort = GetIntSetting(nameof(AppSettings.Port), 587);
        }
    }

    private string MaskSensitiveValue(string key, string? value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return "<null>";
        }

        if (key.IndexOf("Password", StringComparison.OrdinalIgnoreCase) >= 0)
        {
            return "***";
        }

        return value;
    }
}
