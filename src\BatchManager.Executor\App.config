<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- 基本設定 -->
    <add key="InstanceName" value="BatchManagerNet" />
    <add key="CustomerName" value="サンプル顧客株式会社" />
    <add key="BatchDescription" value="バッチ処理システム - テスト環境" />
    <add key="ConfigFilePath" value="BatchManagerNet.config" />
    <add key="LogFilePath" value="BatchManagerNet.log" />
    
    <!-- ログ設定 -->
    <add key="LogLevel" value="Information" />
    <!-- LogLevel: Verbose / Debug / Information / Warning / Error / Fatal -->
    <add key="LogFormat" value="Text" />
    <!-- LogFormat: Text / JSON -->
    
    <!-- ログローテーション設定 -->
    <add key="LogRotationEnabled" value="true" />
    <add key="LogRetentionDays" value="30" />
    <add key="LogArchiveFolder" value="Archive" />
    <add key="LogRotationTime" value="00:00" />
    
    <!-- メール設定 -->
    <add key="EmailEnabled" value="true" />
    <add key="SmtpHost" value="smtp.alpha-prm.jp" />
    <add key="SmtpPort" value="587" />
    <add key="SmtpEnableSsl" value="true" />
    <add key="SmtpUsername" value="noreply%rtsc.co.jp" />
    <add key="SmtpPassword" value="al_noreply001" />
    <add key="EmailFrom" value="<EMAIL>" />
    <add key="EmailTo" value="<EMAIL>,<EMAIL>" />
    <add key="EmailSubjectPrefix" value="[BatchManagerNet]" />
    
    <!-- データベース連携設定 -->
    <add key="DatabaseEnabled" value="true" />
    <add key="DbConnectionString" value="Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true" />
    
    <!-- DB転送設定（バッチ完了後に一括転送） -->
    <add key="DbUploadMode" value="AfterCompletion" />
    <!-- DbUploadMode: AfterCompletion / Disabled -->
    <add key="DbUploadAsync" value="false" />
    <!-- 非同期転送（trueの場合、バッチ終了後に別スレッドで転送） -->
    <add key="DbUploadFailAction" value="SaveAndContinue" />
    <!-- DbUploadFailAction: SaveAndContinue（一時ファイル保存）/ LogOnly（ログのみ） -->
    <add key="DbUploadRetryCount" value="3" />
    <add key="DbUploadRetryInterval" value="5" />
    <!-- リトライ間隔（秒） -->
    <add key="DbUploadTempFolder" value="UploadQueue" />
    <add key="DbUploadTimeout" value="30" />
    <!-- タイムアウト（秒） -->
  </appSettings>
</configuration>

