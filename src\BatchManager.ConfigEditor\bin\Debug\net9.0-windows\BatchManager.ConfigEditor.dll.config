<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- BatchConfigEditor 起動時に既定で読み込むジョブ設定ファイル -->
    <add key="DefaultConfigPath" value="C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0\BatchManagerNet.config" />

    <!-- エディター自身のログ出力先 -->
    <add key="LogFilePath" value="logs\BatchConfigEditor.log" />
    <add key="LogLevel" value="Information" />
    <add key="LogFormat" value="Text" />
  </appSettings>
</configuration>

