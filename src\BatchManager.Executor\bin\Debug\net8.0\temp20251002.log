{"@t":"2025-10-01T22:34:42.1183932Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T22:34:42.1627447Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T22:34:42.1672277Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T22:34:42.1682484Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T22:34:42.1690277Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T22:34:42.1796101Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T22:34:42.1800557Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T22:34:42.1805071Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.1808667Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T22:34:42.1820426Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:34:42.1825430Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T22:34:42.1832632Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T22:34:42.1841932Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.1846039Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T22:34:42.1849938Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T22:34:42.1854069Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T22:34:42.2324078Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T22:34:42.2332672Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T22:34:42.2339016Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T22:34:42.2348655Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T22:34:42.2379100Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.2385974Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T22:34:42.2390200Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T22:34:42.2401459Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T22:34:42.2408702Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T22:34:42.2412945Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T22:34:42.2419745Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T22:34:42.2425843Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T22:34:42.2430717Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T22:34:42.2435473Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T22:34:42.2440153Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T22:34:42.2444668Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T22:34:42.2451364Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T22:34:42.2455824Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.4262184Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T22:57:43.4571008Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T22:57:43.4617921Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T22:57:43.4627519Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T22:57:43.4630501Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T22:57:43.4633283Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T22:57:43.4635760Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T22:57:43.4641319Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.4650066Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.4654086Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.4658147Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T22:57:43.4662941Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T22:57:43.4670116Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.4674603Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T22:57:43.4678668Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T22:57:43.4682550Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T22:57:43.5111289Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T22:57:43.5121397Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T22:57:43.5129066Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T22:57:43.5136606Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T22:57:43.5164068Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.5168585Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T22:57:43.5172682Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T22:57:43.5177535Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T22:57:43.5181602Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T22:57:43.5185746Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T22:57:43.5189820Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T22:57:43.5193861Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T22:57:43.5198032Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T22:57:43.5202101Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T22:57:43.5206174Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T22:57:43.5210177Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T22:57:43.5216774Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T22:57:43.5221346Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.7384410Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T23:29:14.7756884Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T23:29:14.7782932Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T23:29:14.7827961Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T23:29:14.7837901Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T23:29:14.7845370Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T23:29:14.7851805Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T23:29:14.7858418Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.7866082Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.7873953Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.7878516Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T23:29:14.7884781Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T23:29:14.7893295Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.7898851Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T23:29:14.7903885Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T23:29:14.7910045Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T23:29:14.8391841Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T23:29:14.8401265Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T23:29:14.8406600Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T23:29:14.8416997Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T23:29:14.8447627Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.8498507Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T23:29:14.8510321Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T23:29:14.8516686Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T23:29:14.8521450Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T23:29:14.8525995Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T23:29:14.8530447Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T23:29:14.8534845Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T23:29:14.8539166Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T23:29:14.8543454Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T23:29:14.8547844Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T23:29:14.8552077Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T23:29:14.8559366Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T23:29:14.8564132Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:55:30.6165087Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T23:55:30.6624915Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T23:55:30.6665644Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T23:55:30.6672391Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T23:55:30.6677040Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T23:55:30.6681770Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T23:55:30.6685985Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T23:55:30.6691024Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:55:30.6695790Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T23:55:30.6700438Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:55:30.6727133Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T23:55:30.6731523Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T23:55:30.6747144Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:55:30.6752990Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T23:55:30.6757447Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T23:55:30.6762038Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T23:55:30.7301889Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T23:55:30.7316032Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T23:55:30.7321154Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T23:55:30.7334309Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T23:55:30.7373145Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:55:30.7388347Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T23:55:30.7400086Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T23:55:30.7429474Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T23:55:30.7436543Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T23:55:30.7445091Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T23:55:30.7450108Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T23:55:30.7455781Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T23:55:30.7460686Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T23:55:30.7465307Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T23:55:30.7469987Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T23:55:30.7474430Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T23:55:30.7482354Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T23:55:30.7488206Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-02T00:00:41.7976044Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-02T00:00:41.8441034Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-02T00:00:41.8488085Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-02T00:00:41.8516068Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-02T00:00:41.8527402Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-02T00:00:41.8536116Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-02T00:00:41.8543466Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-02T00:00:41.8552157Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-02T00:00:41.8556739Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-02T00:00:41.8566208Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-02T00:00:41.8576084Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-02T00:00:41.8586504Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-02T00:00:41.8599466Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-02T00:00:41.8607621Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-02T00:00:41.8615975Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-02T00:00:41.8623949Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-02T00:00:41.9185229Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-02T00:00:41.9208991Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-02T00:00:41.9221681Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-02T00:00:41.9236355Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-02T00:00:41.9273629Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-02T00:00:41.9281034Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-02T00:00:41.9288098Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-02T00:00:41.9296577Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-02T00:00:41.9306959Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-02T00:00:41.9317341Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-02T00:00:41.9328349Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-02T00:00:41.9349831Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-02T00:00:41.9359509Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-02T00:00:41.9367240Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-02T00:00:41.9373863Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-02T00:00:41.9380174Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-02T00:00:41.9389860Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-02T00:00:41.9396476Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
