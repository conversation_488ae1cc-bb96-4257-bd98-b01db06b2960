using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows;

namespace BatchManager.ConfigEditor.ViewModels;

/// <summary>
/// メイン画面のViewModel
/// </summary>
public partial class MainViewModel : ObservableObject
{
    private readonly IConfigService _configService;
    private readonly ILogService _logService;
    private readonly EditorConfigPaths _configPaths;
    private string? _currentConfigFilePath;

    [ObservableProperty]
    private ObservableCollection<ComList> _jobs = new();

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(DeleteJobCommand), nameof(MoveJobUpCommand), nameof(MoveJobDownCommand))]
    private ComList? _selectedJob;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(SaveConfigFileCommand))]
    private bool _isModified = false;

    [ObservableProperty]
    private string _statusMessage = "設定ファイルを開いてください";

    public MainViewModel(IConfigService configService, ILogService logService, EditorConfigPaths configPaths)
    {
        _configService = configService;
        _logService = logService;
        _configPaths = configPaths;
        
        // Jobsコレクションの変更を監視
        Jobs.CollectionChanged += (s, e) => 
        {
            IsModified = true;
        };
    }

    /// <summary>
    /// 設定ファイルを開く
    /// </summary>
    [RelayCommand]
    private async Task OpenConfigFileAsync()
    {
        try
        {
            var dialog = new OpenFileDialog
            {
                Filter = "設定ファイル (*.config)|*.config|すべてのファイル (*.*)|*.*",
                Title = "BatchManagerNet.configを選択"
            };

            if (dialog.ShowDialog() == true)
            {
                await LoadConfigFileAsync(dialog.FileName);
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "設定ファイルを開く際にエラーが発生しました");
            MessageBox.Show($"設定ファイルを開く際にエラーが発生しました:\n{ex.Message}", 
                "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 設定ファイルを読み込む
    /// </summary>
    private async Task LoadConfigFileAsync(string filePath)
    {
        try
        {
            StatusMessage = "読み込み中...";

            var config = await _configService.LoadJobConfigAsync(filePath);

            Jobs.Clear();
            foreach (var job in config.ComLists)
            {
                // 各ジョブのプロパティ変更を監視
                job.PropertyChanged += (s, e) => IsModified = true;
                Jobs.Add(job);
            }

            _currentConfigFilePath = filePath;
            IsModified = false;
            StatusMessage = $"読み込み完了: {Jobs.Count}件のジョブ";

            _logService.Information($"設定ファイルを読み込みました: {filePath}");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "設定ファイルの読み込みに失敗しました");
            StatusMessage = "読み込み失敗";
            throw;
        }
    }

    /// <summary>
    /// 設定ファイルを保存
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveConfigFileAsync()
    {
        try
        {
            if (string.IsNullOrEmpty(_currentConfigFilePath))
            {
                await SaveConfigFileAsAsync();
                return;
            }

            StatusMessage = "保存中...";

            var config = new ComListArray
            {
                ComLists = Jobs.ToArray()
            };

            await _configService.SaveJobConfigAsync(_currentConfigFilePath, config);

            IsModified = false;
            StatusMessage = $"保存完了: {Jobs.Count}件のジョブ";

            _logService.Information($"設定ファイルを保存しました: {_currentConfigFilePath}");

            MessageBox.Show("設定ファイルを保存しました。", "保存完了", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "設定ファイルの保存に失敗しました");
            StatusMessage = "保存失敗";
            MessageBox.Show($"設定ファイルの保存に失敗しました:\n{ex.Message}", 
                "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private bool CanSave() => !string.IsNullOrEmpty(_currentConfigFilePath) && IsModified;

    /// <summary>
    /// 名前を付けて保存
    /// </summary>
    [RelayCommand]
    private async Task SaveConfigFileAsAsync()
    {
        try
        {
            var dialog = new SaveFileDialog
            {
                Filter = "設定ファイル (*.config)|*.config|すべてのファイル (*.*)|*.*",
                Title = "名前を付けて保存",
                FileName = "BatchManagerNet.config"
            };

            if (dialog.ShowDialog() == true)
            {
                _currentConfigFilePath = dialog.FileName;
                await SaveConfigFileAsync();
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "名前を付けて保存する際にエラーが発生しました");
            MessageBox.Show($"名前を付けて保存する際にエラーが発生しました:\n{ex.Message}", 
                "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// ジョブを追加
    /// </summary>
    [RelayCommand]
    private void AddJob()
    {
        var newJob = new ComList
        {
            ComLine = "C:\\",
            Param = "",
            Type = 2,
            LimitTime = 300,
            EndFlg = 1,
            Description = "新しいジョブ",
            Enabled = true,
            RetryCount = 0,
            NotifyOnSuccess = false
        };

        // 新しいジョブのプロパティ変更を監視
        newJob.PropertyChanged += (s, e) => IsModified = true;
        
        Jobs.Add(newJob);
        SelectedJob = newJob;
        IsModified = true;
        StatusMessage = $"ジョブを追加しました（合計: {Jobs.Count}件）";
    }

    /// <summary>
    /// 選択されたジョブを削除
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDeleteJob))]
    private void DeleteJob()
    {
        if (SelectedJob != null)
        {
            var result = MessageBox.Show(
                $"ジョブ「{SelectedJob.Description}」を削除しますか?",
                "確認", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Jobs.Remove(SelectedJob);
                IsModified = true;
                StatusMessage = $"ジョブを削除しました（残り: {Jobs.Count}件）";
            }
        }
    }

    private bool CanDeleteJob() => SelectedJob != null;

    /// <summary>
    /// 選択されたジョブを上に移動
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanMoveJobUp))]
    private void MoveJobUp()
    {
        if (SelectedJob != null)
        {
            var index = Jobs.IndexOf(SelectedJob);
            if (index > 0)
            {
                Jobs.Move(index, index - 1);
                IsModified = true;
                StatusMessage = "ジョブを上に移動しました";
            }
        }
    }

    private bool CanMoveJobUp() => SelectedJob != null && Jobs.IndexOf(SelectedJob) > 0;

    /// <summary>
    /// 選択されたジョブを下に移動
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanMoveJobDown))]
    private void MoveJobDown()
    {
        if (SelectedJob != null)
        {
            var index = Jobs.IndexOf(SelectedJob);
            if (index < Jobs.Count - 1)
            {
                Jobs.Move(index, index + 1);
                IsModified = true;
                StatusMessage = "ジョブを下に移動しました";
            }
        }
    }

    private bool CanMoveJobDown() => SelectedJob != null && Jobs.IndexOf(SelectedJob) < Jobs.Count - 1;

    /// <summary>
    /// 起動時に設定ファイルを読み込む
    /// </summary>
    public async Task InitializeAsync(string? configFilePath = null)
    {
        try
        {
            if (!string.IsNullOrEmpty(configFilePath) && File.Exists(configFilePath))
            {
                _logService.Information($"コマンドライン引数で指定された設定ファイルを読み込みます: {configFilePath}");
                await LoadConfigFileAsync(configFilePath);
            }
            else
            {
                _logService.Information($"既定の設定ファイルパス: {_configPaths.DefaultConfigPath ?? "(null)"}");

                var targetPath = GetExistingPath(_configPaths.DefaultConfigPath, "DefaultConfigPath");

                if (targetPath != null)
                {
                    await LoadConfigFileAsync(targetPath);
                }
                else
                {
                    _logService.Warning("読み込む設定ファイルが見つかりませんでした。空の状態で起動します。");
                    StatusMessage = "設定ファイルが見つかりませんでした";
                }
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "初期化時のファイル読み込みに失敗しました");
        }
    }

    private string? GetExistingPath(string? path, string label)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            _logService.Debug($"{label}: パスが未設定です");
            return null;
        }

        if (File.Exists(path))
        {
            _logService.Information($"{label}: ファイルを検出しました -> {path}");
            return path;
        }

        _logService.Warning($"{label}: ファイルが見つかりません -> {path}");
        return null;
    }
}

