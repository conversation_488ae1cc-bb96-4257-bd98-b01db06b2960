{"format": 1, "restore": {"C:\\projects\\BatchManager\\src\\BatchManager.Core\\BatchManager.Core.csproj": {}}, "projects": {"C:\\projects\\BatchManager\\src\\BatchManager.Core\\BatchManager.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\projects\\BatchManager\\src\\BatchManager.Core\\BatchManager.Core.csproj", "projectName": "BatchManager.Core", "projectPath": "C:\\projects\\BatchManager\\src\\BatchManager.Core\\BatchManager.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\projects\\BatchManager\\src\\BatchManager.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Dapper": {"target": "Package", "version": "[2.1.66, )"}, "MailKit": {"target": "Package", "version": "[4.14.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.9, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Formatting.Compact": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[9.0.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}