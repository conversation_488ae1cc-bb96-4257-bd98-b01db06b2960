{"@t":"2025-10-01T22:34:42.2465341Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T22:34:42.2615090Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T22:34:42.2621513Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T22:34:42.2625865Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T22:34:42.2629980Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T22:34:42.2638469Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T22:34:42.2642054Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T22:34:42.2645601Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T22:34:42.2649033Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.2652693Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T22:34:42.2656710Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:34:42.2660629Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T22:34:42.2663944Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T22:34:42.2667237Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.2670449Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T22:34:42.2673723Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T22:34:42.2677090Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T22:34:42.2680352Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T22:34:42.2687069Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T22:34:42.2691523Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T22:34:42.2694822Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T22:34:42.2697957Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:34:42.2701044Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T22:34:42.2704132Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T22:34:42.2707540Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T22:34:42.2710507Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T22:34:42.2713532Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T22:34:42.2716680Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T22:34:42.2720094Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T22:34:42.2723235Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T22:34:42.2726284Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T22:34:42.2743755Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T22:34:42.2747756Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T22:34:42.2756432Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T22:34:42.2761142Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:34:42.2768015Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T22:34:42.2771879Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T22:34:42.2825420Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T22:34:42.2850098Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T22:34:42.3182223Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T22:34:42.3196723Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T22:34:42.3221803Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T22:34:42.3226940Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T22:34:42.3276161Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T22:34:42.3282493Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T22:34:42.3287888Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T22:34:42.3308360Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T22:34:42.3322653Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T22:34:42.5959161Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"System.Net.Sockets.SocketException (11004): 要求した名前は有効ですが、要求された種類のデータは見つかりませんでした。\r\n   at System.Net.NameResolutionPal.ProcessResult(SocketError errorCode, GetAddrInfoExContext* context)\r\n   at System.Net.NameResolutionPal.GetAddressInfoExCallback(Int32 error, Int32 bytes, NativeOverlapped* overlapped)\r\n--- End of stack trace from previous location ---\r\n   at MailKit.Net.SocketUtils.ConnectAsync(String host, Int32 port, IPEndPoint localEndPoint, CancellationToken cancellationToken)\r\n   at MailKit.Net.SocketUtils.ConnectAsync(String host, Int32 port, IPEndPoint localEndPoint, Int32 timeout, CancellationToken cancellationToken)\r\n   at MailKit.MailService.ConnectNetworkAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 73"}
{"@t":"2025-10-01T22:34:42.6058100Z","@mt":"エラーメール送信に失敗しました: 要求した名前は有効ですが、要求された種類のデータは見つかりませんでした。","@l":"Warning"}
{"@t":"2025-10-01T22:34:42.6080543Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T22:34:42.6089496Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T22:34:42.6093943Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T22:34:42.6097342Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T22:34:42.6100542Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T22:34:42.6103479Z","@mt":"  失敗: 1"}
{"@t":"2025-10-01T22:34:42.6117094Z","@mt":"  実行時間: 0.33秒"}
{"@t":"2025-10-01T22:34:42.6121325Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T22:34:42.6130725Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T22:34:44.5940921Z","@mt":"バッチ実行履歴を登録しました (ID: 22)"}
{"@t":"2025-10-01T22:34:44.8620385Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T22:34:44.8728531Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T22:34:44.9364754Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T22:34:44.9584424Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T22:57:43.5230220Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T22:57:43.5402493Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T22:57:43.5405804Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T22:57:43.5408307Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T22:57:43.5410669Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T22:57:43.5413153Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T22:57:43.5433009Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T22:57:43.5436047Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T22:57:43.5438668Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.5441016Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.5443674Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.5485125Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T22:57:43.5488527Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T22:57:43.5491533Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.5494383Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T22:57:43.5497231Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T22:57:43.5500059Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T22:57:43.5503071Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T22:57:43.5505933Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T22:57:43.5508867Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T22:57:43.5511790Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T22:57:43.5515321Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T22:57:43.5518248Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T22:57:43.5521773Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T22:57:43.5525745Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T22:57:43.5529361Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T22:57:43.5533751Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T22:57:43.5537230Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T22:57:43.5540643Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T22:57:43.5544152Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T22:57:43.5547622Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T22:57:43.5551243Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T22:57:43.5555008Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T22:57:43.5558311Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T22:57:43.5561941Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T22:57:43.5567989Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T22:57:43.5570698Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T22:57:43.5641527Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T22:57:43.5667258Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T22:57:43.5935964Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T22:57:43.5955310Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T22:57:43.5968956Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T22:57:43.5982722Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T22:57:43.5989041Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T22:57:43.5994762Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T22:57:43.6000171Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T22:57:43.6018909Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T22:57:43.6032349Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T22:57:43.7874364Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"System.Net.Sockets.SocketException (11004): 要求した名前は有効ですが、要求された種類のデータは見つかりませんでした。\r\n   at System.Net.NameResolutionPal.ProcessResult(SocketError errorCode, GetAddrInfoExContext* context)\r\n   at System.Net.NameResolutionPal.GetAddressInfoExCallback(Int32 error, Int32 bytes, NativeOverlapped* overlapped)\r\n--- End of stack trace from previous location ---\r\n   at MailKit.Net.SocketUtils.ConnectAsync(String host, Int32 port, IPEndPoint localEndPoint, CancellationToken cancellationToken)\r\n   at MailKit.Net.SocketUtils.ConnectAsync(String host, Int32 port, IPEndPoint localEndPoint, Int32 timeout, CancellationToken cancellationToken)\r\n   at MailKit.MailService.ConnectNetworkAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 73"}
{"@t":"2025-10-01T22:57:43.8028361Z","@mt":"エラーメール送信に失敗しました: 要求した名前は有効ですが、要求された種類のデータは見つかりませんでした。","@l":"Warning"}
{"@t":"2025-10-01T22:57:43.8033342Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T22:57:43.8039902Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T22:57:43.8044530Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T22:57:43.8048525Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T22:57:43.8052944Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T22:57:43.8057152Z","@mt":"  失敗: 1"}
{"@t":"2025-10-01T22:57:43.8071306Z","@mt":"  実行時間: 0.24秒"}
{"@t":"2025-10-01T22:57:43.8075110Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T22:57:43.8084947Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T22:57:45.2833582Z","@mt":"バッチ実行履歴を登録しました (ID: 23)"}
{"@t":"2025-10-01T22:57:45.5608382Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T22:57:45.5633117Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T22:57:45.5646387Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T22:57:45.5658138Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T23:29:14.8574087Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T23:29:14.8748642Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T23:29:14.8755621Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T23:29:14.8761966Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T23:29:14.8768690Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T23:29:14.8770834Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T23:29:14.8777767Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T23:29:14.8783672Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T23:29:14.8795029Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.8801664Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.8805974Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.8809447Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T23:29:14.8840347Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T23:29:14.8848043Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.8853448Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T23:29:14.8858333Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T23:29:14.8862930Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T23:29:14.8867457Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T23:29:14.8871934Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T23:29:14.8876251Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T23:29:14.8880662Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T23:29:14.8885161Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T23:29:14.8889432Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T23:29:14.8893587Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T23:29:14.8898094Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T23:29:14.8902190Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T23:29:14.8906273Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T23:29:14.8910341Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T23:29:14.8914352Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T23:29:14.8918202Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T23:29:14.8921889Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T23:29:14.8926229Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T23:29:14.8930149Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T23:29:14.8936176Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T23:29:14.8939394Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T23:29:14.8946595Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T23:29:14.8950822Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T23:29:14.9010657Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T23:29:14.9025951Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T23:29:14.9313798Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T23:29:14.9324264Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T23:29:14.9345387Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T23:29:14.9351399Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T23:29:14.9359993Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T23:29:14.9380648Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T23:29:14.9384995Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T23:29:14.9403408Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T23:29:14.9420869Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T23:29:16.5213539Z","@mt":"メールを送信しました: 【エラー通知】BatchManagerNet - bat5"}
{"@t":"2025-10-01T23:29:16.5233033Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T23:29:16.5250878Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T23:29:16.5260052Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T23:29:16.5355070Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T23:29:16.5364061Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T23:29:16.5371419Z","@mt":"  失敗: 1"}
{"@t":"2025-10-01T23:29:16.5394510Z","@mt":"  実行時間: 1.62秒"}
{"@t":"2025-10-01T23:29:16.5401523Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T23:29:16.5416451Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T23:29:18.8002429Z","@mt":"バッチ実行履歴を登録しました (ID: 24)"}
{"@t":"2025-10-01T23:29:19.0624513Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T23:29:19.1102833Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T23:29:19.1109298Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T23:29:19.1117136Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
