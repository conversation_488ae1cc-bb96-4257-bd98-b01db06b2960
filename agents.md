# BatchManagerNet 開発プロジェクト - エージェント定義書

## 概要

本ドキュメントは、BatchManagerNetプロジェクトの開発において、複数のAIエージェントが協調して作業を進めるための役割分担と責務を定義します。

**プロジェクト名：** BatchManagerNet 最新化プロジェクト  
**バージョン：** 1.0  
**作成日：** 2025年10月1日  
**参照ドキュメント：** 要件定義書_BatchManagerNet.md v1.2

---

## エージェント一覧

| エージェントID | 役割 | 主な担当領域 |
|---------------|------|-------------|
| PM | プロジェクトマネージャー | 全体管理、進捗管理、調整 |
| ARCH | アーキテクト | システム設計、技術選定 |
| CORE-DEV | コア開発 | BatchManager.Core開発 |
| EXEC-DEV | バッチ実行開発 | BatchManager.Executor開発 |
| UI-DEV | UI開発 | BatchManager.ConfigEditor開発 |
| DB-ENG | データベースエンジニア | DB設計、クエリ最適化 |
| QA | 品質保証 | テスト設計・実行 |
| DOC | ドキュメンテーション | マニュアル・ドキュメント作成 |
| DEVOPS | DevOps | ビルド、デプロイ、CI/CD |

---

## 1. プロジェクトマネージャー（PM）

### 役割
プロジェクト全体の統括、進捗管理、エージェント間の調整

### 責務
- プロジェクト全体のスケジュール管理
- 各エージェントへのタスク割り当て
- エージェント間の依存関係の調整
- リスク管理と課題解決
- ステークホルダーへの報告
- 開発フェーズの進行管理（フェーズ1～5）
- 成果物のレビューと承認

### 担当範囲
- プロジェクト計画全体
- 要件定義書の管理
- マイルストーン管理

### 成果物
- プロジェクト計画書
- 週次進捗レポート
- リスク管理表
- タスク管理表
- エージェント間連携スケジュール

### 連携するエージェント
- 全エージェント（調整役）

### 主要タスク

#### フェーズ1：基盤構築（1-2週間）
- [ ] 開発環境のセットアップ確認（DEVOPS）
- [ ] プロジェクト構成の確認（ARCH）
- [ ] タスクの割り振り

#### フェーズ2：新機能開発（2-3週間）
- [ ] 各エージェントの進捗確認
- [ ] 依存関係の調整

#### フェーズ3：GUI開発（2-3週間）
- [ ] UI開発の進捗管理
- [ ] 統合テストの準備

#### フェーズ4：テスト・デバッグ（1-2週間）
- [ ] テスト結果のレビュー
- [ ] バグ修正の優先順位付け

#### フェーズ5：ドキュメント作成・リリース（1週間）
- [ ] ドキュメントのレビュー
- [ ] リリース判定

---

## 2. アーキテクト（ARCH）

### 役割
システム全体のアーキテクチャ設計、技術選定、設計ガイドライン策定

### 責務
- システムアーキテクチャの設計
- 技術スタックの選定と推奨
- プロジェクト構成の設計
- クラス設計の基本方針策定
- デザインパターンの適用
- 非機能要件の技術的実現方法の検討
- コーディング規約の策定

### 担当範囲
- システム全体のアーキテクチャ
- 分離型アーキテクチャ（BatchManagerNet.exe + BatchConfigEditor.exe）
- BatchManager.Coreの設計方針
- DB転送方式の技術的詳細設計
- インターフェース設計

### 成果物
- システムアーキテクチャ設計書
- クラス図
- シーケンス図
- インターフェース定義書
- コーディング規約
- 技術選定書（NuGetパッケージ一覧含む）

### 連携するエージェント
- PM（設計承認依頼）
- CORE-DEV、EXEC-DEV、UI-DEV（設計ガイドライン提供）
- DB-ENG（DB設計方針の調整）

### 主要タスク

#### 初期設計
- [ ] 詳細アーキテクチャ設計書の作成
- [ ] BatchManager.Coreのクラス設計
- [ ] インターフェース設計（IConfigService、IDatabaseService等）
- [ ] DB転送方式の実装パターン選定（パターンA/B）
- [ ] 依存性注入（DI）の設計
- [ ] エラーハンドリング方針の策定

#### 継続タスク
- [ ] 各開発エージェントへの技術支援
- [ ] コードレビュー（アーキテクチャ観点）
- [ ] 設計書の更新・維持

---

## 3. コア開発（CORE-DEV）

### 役割
共通ライブラリ（BatchManager.Core.dll）の開発

### 責務
- データモデルの実装
- サービス層の実装
- インターフェースの実装
- ユーティリティクラスの実装
- 共通ロジックの実装
- 単体テストの作成（自己責任範囲）

### 担当範囲
- `BatchManager.Core`プロジェクト全体
  - Models/
  - Services/
  - Interfaces/
  - Utilities/

### 成果物
- BatchManager.Core.dll
- 単体テストコード（BatchManager.Core.Tests）
- XMLドキュメントコメント
- 実装ドキュメント

### 連携するエージェント
- ARCH（設計確認）
- EXEC-DEV、UI-DEV（インターフェース調整）
- DB-ENG（DatabaseService連携）
- QA（テスト仕様確認）

### 主要タスク

#### Models実装
- [ ] ComList.cs（ジョブ設定モデル）
- [ ] BatchExecution.cs（バッチ実行履歴モデル）
- [ ] JobExecution.cs（ジョブ実行履歴モデル）
- [ ] AppSettings.cs（設定モデル）

#### Services実装
- [ ] ConfigService.cs（設定ファイル読み書き）
- [ ] LogService.cs（Serilog統合）
- [ ] DatabaseService.cs（DB接続・CRUD）
- [ ] DatabaseUploadService.cs（DB一括アップロード）
- [ ] UploadQueueService.cs（転送キュー管理）
- [ ] EmailService.cs（MailKit統合）
- [ ] LogRotationService.cs（ログローテーション）

#### Interfaces実装
- [ ] IConfigService.cs
- [ ] ILogService.cs
- [ ] IDatabaseService.cs
- [ ] IEmailService.cs

#### Utilities実装
- [ ] ProcessHelper.cs（プロセス管理）
- [ ] ValidationHelper.cs（バリデーション）

#### テスト
- [ ] 各サービスの単体テスト
- [ ] モック作成

---

## 4. バッチ実行開発（EXEC-DEV）

### 役割
バッチ実行エンジン（BatchManagerNet.exe）の開発

### 責務
- コンソールアプリケーションの実装
- バッチ実行フローの実装
- ジョブ実行・監視の実装
- タイムアウト検知の実装
- DB転送調整の実装
- コマンドライン引数の処理
- 単体テストの作成（自己責任範囲）

### 担当範囲
- `BatchManager.Executor`プロジェクト全体
- BatchManagerNet.exe.configの設計

### 成果物
- BatchManagerNet.exe
- App.config（BatchManagerNet.exe.config）
- 単体テストコード（BatchManager.Executor.Tests）
- 実装ドキュメント

### 連携するエージェント
- ARCH（設計確認）
- CORE-DEV（共通ライブラリ利用）
- DB-ENG（DB転送処理確認）
- QA（テスト仕様確認）

### 主要タスク

#### 基本実装
- [ ] Program.cs（エントリーポイント、コマンドライン引数処理）
- [ ] BatchRunner.cs（バッチ実行制御、4フェーズ実装）
- [ ] JobExecutor.cs（ジョブ実行・監視）
- [ ] TimeoutMonitor.cs（タイムアウト検知）
- [ ] ExecutionResultCollector.cs（実行結果の蓄積）
- [ ] UploadCoordinator.cs（DB転送調整）

#### 機能実装
- [ ] 設定ファイル読み込み
- [ ] ログローテーションチェック
- [ ] 前回失敗DB転送データの再送信
- [ ] ジョブ順次実行
- [ ] タイムアウト処理（endFlg 1-4）
- [ ] メール通知
- [ ] DB転送（別スレッド/非同期）
- [ ] 終了コード制御

#### テスト
- [ ] 正常系テスト
- [ ] タイムアウトテスト
- [ ] DB転送失敗テスト
- [ ] リトライテスト

---

## 5. UI開発（UI-DEV）

### 役割
GUI設定エディター（BatchConfigEditor.exe）の開発

### 責務
- WPFアプリケーションの実装
- MVVM パターンの実装
- 画面デザインの実装
- データバインディングの実装
- バリデーション機能の実装
- 統計情報表示機能の実装
- 単体テストの作成（自己責任範囲）

### 担当範囲
- `BatchManager.ConfigEditor`プロジェクト全体
- BatchConfigEditor.exe.configの設計

### 成果物
- BatchConfigEditor.exe
- XAMLファイル一式
- App.config（BatchConfigEditor.exe.config）
- 単体テストコード（BatchManager.ConfigEditor.Tests）
- UI実装ドキュメント

### 連携するエージェント
- ARCH（設計確認、UIパターン確認）
- CORE-DEV（共通ライブラリ利用）
- DB-ENG（統計情報取得確認）
- QA（UI/UXテスト）

### 主要タスク

#### Views実装
- [ ] MainWindow.xaml（メイン画面）
- [ ] JobEditDialog.xaml（ジョブ編集ダイアログ）
- [ ] StatisticsView.xaml（統計情報タブ）
- [ ] SettingsView.xaml（設定タブ）

#### ViewModels実装
- [ ] MainViewModel.cs
- [ ] JobEditViewModel.cs
- [ ] StatisticsViewModel.cs
- [ ] SettingsViewModel.cs

#### 機能実装
- [ ] ジョブ一覧表示（DataGrid）
- [ ] ジョブ追加・編集・削除
- [ ] ジョブ並び替え（ドラッグ&ドロップ、↑↓ボタン）
- [ ] 設定ファイル読み込み・保存
- [ ] バリデーション（ファイル存在チェック等）
- [ ] XMLプレビュー機能
- [ ] 統計情報取得・表示（グラフ）
- [ ] CSVエクスポート

#### テスト
- [ ] ViewModelテスト
- [ ] UIテスト（手動）

---

## 6. データベースエンジニア（DB-ENG）

### 役割
データベース設計、スキーマ作成、クエリ最適化

### 責務
- データベーススキーマの設計・作成
- インデックス設計
- ビュー設計
- 統計クエリの作成・最適化
- Supabaseセットアップ
- データベース接続テスト
- マイグレーション管理

### 担当範囲
- データベーススキーマ（PostgreSQL）
- database/ディレクトリ
- 統計情報用ビュー
- クエリ最適化

### 成果物
- schema.sql（テーブル定義、インデックス、ビュー）
- sample_data.sql（テストデータ）
- クエリ集（統計情報用）
- Supabaseセットアップガイド
- マイグレーションスクリプト

### 連携するエージェント
- ARCH（DB設計方針確認）
- CORE-DEV（DatabaseService連携）
- EXEC-DEV（DB転送処理確認）
- UI-DEV（統計クエリ提供）
- QA（DBテスト）

### 主要タスク

#### スキーマ設計
- [ ] batch_executionsテーブル作成
- [ ] job_executionsテーブル作成
- [ ] インデックス作成（4種類）
- [ ] 外部キー制約設定
- [ ] v_job_statisticsビュー作成

#### Supabaseセットアップ
- [ ] Supabaseプロジェクト作成
- [ ] スキーマ実行
- [ ] 接続情報取得
- [ ] Row Level Security（RLS）設定（オプション）

#### クエリ作成
- [ ] 統計情報取得クエリ
  - 直近30日の成功率
  - ジョブ別平均実行時間
  - タイムアウト発生頻度
  - ジョブ別詳細統計
- [ ] パフォーマンス最適化
- [ ] クエリドキュメント作成

#### テストデータ作成
- [ ] サンプルデータ作成
- [ ] テストケース用データ

---

## 7. 品質保証（QA）

### 役割
テスト計画策定、テスト実行、品質保証

### 責務
- テスト計画の策定
- テストケースの作成
- 単体テストのレビュー
- 結合テストの実施
- 実環境テストの実施
- パフォーマンステストの実施
- バグ報告・追跡
- テスト結果の報告

### 担当範囲
- 全プロジェクトのテスト
- tests/ディレクトリ
- テストドキュメント

### 成果物
- テスト計画書
- テストケース一覧
- テスト結果レポート
- バグ報告書
- 品質評価レポート

### 連携するエージェント
- PM（テスト計画承認、バグ報告）
- CORE-DEV、EXEC-DEV、UI-DEV（テスト実施、バグ報告）
- DB-ENG（DBテスト）

### 主要タスク

#### テスト計画
- [ ] テスト計画書作成
- [ ] テストケース設計
- [ ] テストデータ準備

#### 単体テスト
- [ ] BatchManager.Coreテストレビュー
- [ ] BatchManager.Executorテストレビュー
- [ ] BatchManager.ConfigEditorテストレビュー

#### 結合テスト
- [ ] バッチ実行フローテスト
- [ ] DB転送テスト
- [ ] ログローテーションテスト
- [ ] メール通知テスト
- [ ] GUI操作テスト

#### 実環境テスト
- [ ] 本番相当環境でのテスト
- [ ] 長時間実行テスト
- [ ] 負荷テスト

#### 性能テスト
- [ ] バッチ実行時間測定
- [ ] DB転送時間測定
- [ ] GUI起動時間測定
- [ ] メモリ使用量測定

#### バグ管理
- [ ] バグトラッキング
- [ ] 再テスト
- [ ] リグレッションテスト

---

## 8. ドキュメンテーション（DOC）

### 役割
ユーザーマニュアル、技術ドキュメントの作成

### 責務
- 操作マニュアルの作成
- セットアップガイドの作成
- リリースノートの作成
- README.mdの作成
- API仕様書の作成（必要に応じて）
- ドキュメントの校正・編集

### 担当範囲
- docs/ディレクトリ
- README.md
- 各種マニュアル

### 成果物
- 操作マニュアル_バッチ実行.md
- 操作マニュアル_エディター.md
- セットアップガイド.md
- README.md
- リリースノート.md
- トラブルシューティングガイド.md
- FAQ.md

### 連携するエージェント
- PM（ドキュメント承認）
- ARCH（技術仕様確認）
- EXEC-DEV、UI-DEV（機能確認）
- DB-ENG（DB設定確認）

### 主要タスク

#### バッチ実行マニュアル
- [ ] インストール手順
- [ ] 設定ファイルの編集方法
- [ ] 実行方法（コマンドライン）
- [ ] タスクスケジューラ設定
- [ ] ログの見方
- [ ] トラブルシューティング

#### エディターマニュアル
- [ ] インストール手順
- [ ] 画面説明
- [ ] ジョブの追加・編集・削除
- [ ] 統計情報の見方
- [ ] エクスポート機能の使い方

#### セットアップガイド
- [ ] システム要件
- [ ] .NET 8インストール
- [ ] Supabaseセットアップ
- [ ] データベース初期設定
- [ ] アプリケーション配置
- [ ] 設定ファイルの初期設定

#### README.md
- [ ] プロジェクト概要
- [ ] 機能一覧
- [ ] クイックスタート
- [ ] ライセンス情報

#### リリースノート
- [ ] バージョン情報
- [ ] 新機能
- [ ] 変更点
- [ ] 既知の問題

---

## 9. DevOps（DEVOPS）

### 役割
ビルド環境構築、デプロイ、CI/CD構築

### 責務
- 開発環境のセットアップ
- ビルドスクリプトの作成
- CI/CDパイプラインの構築（オプション）
- デプロイパッケージの作成
- デプロイ手順の作成
- バージョン管理の支援

### 担当範囲
- ビルド環境
- デプロイ環境
- deploy/ディレクトリ
- CI/CD（GitHub Actions等）

### 成果物
- ビルドスクリプト
- デプロイパッケージ
- デプロイ手順書
- CI/CD設定ファイル（.github/workflows等）

### 連携するエージェント
- PM（環境セットアップ報告）
- 全開発エージェント（ビルド支援）

### 主要タスク

#### 開発環境構築
- [ ] .NET 8 SDK確認・インストール
- [ ] Visual Studio / VS Codeセットアップ
- [ ] NuGetパッケージ復元確認
- [ ] Gitリポジトリセットアップ

#### ビルド設定
- [ ] ソリューションファイル作成
- [ ] プロジェクト参照設定
- [ ] ビルド構成（Debug/Release）
- [ ] 出力ディレクトリ設定

#### デプロイパッケージ作成
- [ ] batch/フォルダ構成
  - BatchManagerNet.exe
  - BatchManager.Core.dll
  - 依存DLL
  - 設定ファイルサンプル
- [ ] editor/フォルダ構成
  - BatchConfigEditor.exe
  - BatchManager.Core.dll
  - 依存DLL
  - 設定ファイルサンプル

#### CI/CD構築（オプション）
- [ ] 自動ビルド設定
- [ ] 自動テスト実行
- [ ] 自動デプロイ

#### バージョン管理
- [ ] Git管理方針策定
- [ ] ブランチ戦略
- [ ] タグ付けルール

---

## エージェント間連携フロー

### フェーズ1：基盤構築

```
PM → ARCH: アーキテクチャ設計依頼
ARCH → PM: 設計書提出
PM → DEVOPS: 環境構築依頼
DEVOPS → PM: 環境構築完了報告
PM → DB-ENG: スキーマ作成依頼
DB-ENG → PM: スキーマ作成完了
PM → CORE-DEV: Core開発開始指示
CORE-DEV → ARCH: 設計確認
CORE-DEV → PM: 開発完了報告
```

### フェーズ2：新機能開発

```
PM → EXEC-DEV: バッチ実行開発開始
EXEC-DEV ⇄ CORE-DEV: インターフェース調整
EXEC-DEV → DB-ENG: DB転送処理確認
EXEC-DEV → PM: 開発完了報告
PM → QA: 単体テスト依頼
QA → EXEC-DEV: バグ報告
```

### フェーズ3：GUI開発

```
PM → UI-DEV: GUI開発開始
UI-DEV ⇄ CORE-DEV: インターフェース調整
UI-DEV → DB-ENG: 統計クエリ確認
UI-DEV → PM: 開発完了報告
PM → QA: UIテスト依頼
QA → UI-DEV: バグ報告
```

### フェーズ4：テスト・デバッグ

```
PM → QA: 結合テスト開始指示
QA → 全開発エージェント: バグ報告
全開発エージェント → QA: 修正完了報告
QA → PM: テスト結果報告
```

### フェーズ5：ドキュメント作成・リリース

```
PM → DOC: ドキュメント作成依頼
DOC ⇄ 全エージェント: 内容確認
DOC → PM: ドキュメント提出
PM → DEVOPS: デプロイパッケージ作成依頼
DEVOPS → PM: パッケージ完成報告
PM → 全エージェント: リリース完了通知
```

---

## コミュニケーションルール

### 1. 報告・連絡・相談
- 各エージェントは担当タスクの進捗を定期的にPMに報告
- 問題発生時は速やかにPMおよび関連エージェントに報告
- 設計変更が必要な場合はARCHに相談

### 2. ドキュメント管理
- 成果物はすべて所定のディレクトリに配置
- ドキュメントにはバージョン番号と更新日を記載
- 重要な決定事項は記録を残す

### 3. コードレビュー
- 各開発エージェントは相互にコードレビューを実施
- ARCHはアーキテクチャ観点からレビュー
- QAはテスト可能性の観点からレビュー

### 4. 課題管理
- 発見した課題は速やかにPMに報告
- PMが優先順位を決定し、担当エージェントに割り当て
- 課題解決後は関連エージェントに共有

---

## 成果物チェックリスト

### ARCH
- [ ] システムアーキテクチャ設計書
- [ ] クラス図
- [ ] シーケンス図
- [ ] インターフェース定義書
- [ ] コーディング規約

### CORE-DEV
- [ ] BatchManager.Core.dll
- [ ] 単体テストコード
- [ ] XMLドキュメントコメント

### EXEC-DEV
- [ ] BatchManagerNet.exe
- [ ] App.config
- [ ] 単体テストコード

### UI-DEV
- [ ] BatchConfigEditor.exe
- [ ] XAMLファイル一式
- [ ] App.config
- [ ] 単体テストコード

### DB-ENG
- [ ] schema.sql
- [ ] sample_data.sql
- [ ] 統計クエリ集
- [ ] Supabaseセットアップガイド

### QA
- [ ] テスト計画書
- [ ] テストケース一覧
- [ ] テスト結果レポート
- [ ] バグ報告書
- [ ] 品質評価レポート

### DOC
- [ ] 操作マニュアル（バッチ実行）
- [ ] 操作マニュアル（エディター）
- [ ] セットアップガイド
- [ ] README.md
- [ ] リリースノート

### DEVOPS
- [ ] ビルドスクリプト
- [ ] デプロイパッケージ
- [ ] デプロイ手順書

---

## タイムライン

| フェーズ | 期間 | 主担当エージェント | 成果物 |
|---------|------|-------------------|--------|
| フェーズ1 | 1-2週間 | ARCH, CORE-DEV, DB-ENG, DEVOPS | 基盤構築完了 |
| フェーズ2 | 2-3週間 | EXEC-DEV, CORE-DEV, DB-ENG | 新機能実装完了 |
| フェーズ3 | 2-3週間 | UI-DEV, CORE-DEV, DB-ENG | GUI実装完了 |
| フェーズ4 | 1-2週間 | QA, 全開発エージェント | テスト完了 |
| フェーズ5 | 1週間 | DOC, DEVOPS, PM | リリース準備完了 |

**総開発期間：7-11週間**

---

## 付録：エージェント別技術スタック

### CORE-DEV
- C# / .NET 8
- Serilog
- System.Xml.Serialization
- Microsoft.Extensions.Configuration
- Microsoft.Extensions.DependencyInjection

### EXEC-DEV
- C# / .NET 8（コンソールアプリ）
- System.Diagnostics.Process
- System.Threading.Tasks
- Serilog

### UI-DEV
- C# / .NET 8（WPFアプリ）
- XAML
- MVVMパターン
- LiveCharts（グラフ表示）
- EPPlus（Excelエクスポート）

### DB-ENG
- PostgreSQL
- Supabase
- Npgsql
- Dapper
- SQL

### QA
- xUnit / NUnit / MSTest
- Moq（モックライブラリ）
- FluentAssertions
- TestStack.White（WPF UIテスト）

### DOC
- Markdown
- Mermaid（図表作成）
- ScreenToGif（スクリーンショット）

### DEVOPS
- MSBuild / dotnet CLI
- PowerShell / Bash
- GitHub Actions（オプション）
- WiX Toolset（インストーラ作成、オプション）

---

**文書管理情報**
- 作成日：2025年10月1日
- バージョン：1.0
- 作成者：AI Assistant
- 参照：要件定義書_BatchManagerNet.md v1.2

