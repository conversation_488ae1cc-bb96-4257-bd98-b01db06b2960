using BatchManager.Core.Models;

namespace BatchManager.Core.Interfaces;

/// <summary>
/// 設定ファイルの読み書きを行うサービスのインターフェース
/// </summary>
public interface IConfigService
{
    /// <summary>
    /// BatchManagerNet.configからジョブ設定を読み込む
    /// </summary>
    /// <param name="configFilePath">設定ファイルのパス</param>
    /// <returns>ジョブ設定のリスト</returns>
    Task<ComListArray> LoadJobConfigAsync(string configFilePath);

    /// <summary>
    /// BatchManagerNet.configにジョブ設定を保存する
    /// </summary>
    /// <param name="configFilePath">設定ファイルのパス</param>
    /// <param name="config">保存するジョブ設定</param>
    Task SaveJobConfigAsync(string configFilePath, ComListArray config);

    /// <summary>
    /// BatchManagerNet.exe.configからアプリケーション設定を読み込む
    /// </summary>
    /// <returns>アプリケーション設定</returns>
    AppSettings LoadAppSettings();

    /// <summary>
    /// 設定ファイルのバリデーションを行う
    /// </summary>
    /// <param name="configFilePath">設定ファイルのパス</param>
    /// <returns>検証結果（エラーがなければ空のリスト）</returns>
    Task<List<string>> ValidateJobConfigAsync(string configFilePath);
}



