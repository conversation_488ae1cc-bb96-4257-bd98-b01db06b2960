using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;
using System.Diagnostics;

namespace BatchManager.Executor.Services;

/// <summary>
/// 個別ジョブの実行と監視を行うクラス
/// </summary>
public class JobExecutor
{
    private readonly ILogService _logService;

    public JobExecutor(ILogService logService)
    {
        _logService = logService;
    }

    /// <summary>
    /// ジョブを実行する
    /// </summary>
    public async Task<JobExecution> ExecuteJobAsync(ComList job, int jobSequence)
    {
        var startTime = DateTime.Now;
        var jobResult = new JobExecution
        {
            JobSequence = jobSequence,
            JobName = Path.GetFileNameWithoutExtension(job.ComLine),
            CommandLine = job.ComLine,
            Parameters = job.Param,
            StartTime = startTime,
            TimeoutLimitSeconds = job.LimitTime,
            Status = "Running"
        };

        try
        {
            // ファイル存在チェック
            if (!File.Exists(job.ComLine))
            {
                var errorMsg = $"実行ファイルが見つかりません: {job.ComLine}";
                _logService.Error(errorMsg);
                
                jobResult.EndTime = DateTime.Now;
                jobResult.ExecutionTimeSeconds = (decimal)(jobResult.EndTime.Value - startTime).TotalSeconds;
                jobResult.Status = "Failed";
                jobResult.ErrorMessage = errorMsg;
                jobResult.ExitCode = -1;
                
                return jobResult;
            }

            // プロセスの起動設定
            var processStartInfo = new ProcessStartInfo
            {
                FileName = job.ComLine,
                Arguments = job.Param ?? string.Empty,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            _logService.Debug($"プロセス起動: {job.ComLine} {job.Param}");

            using var process = new Process { StartInfo = processStartInfo };
            
            // 出力とエラーの非同期読み取り
            var outputBuilder = new System.Text.StringBuilder();
            var errorBuilder = new System.Text.StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    outputBuilder.AppendLine(e.Data);
                    _logService.Debug($"[STDOUT] {e.Data}");
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    errorBuilder.AppendLine(e.Data);
                    _logService.Warning($"[STDERR] {e.Data}");
                }
            };

            // プロセス開始
            if (!process.Start())
            {
                throw new Exception("プロセスの起動に失敗しました");
            }

            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // タイムアウト付き待機
            var timeoutMilliseconds = job.LimitTime * 1000;
            var completed = await Task.Run(() => process.WaitForExit(timeoutMilliseconds));

            var endTime = DateTime.Now;
            var executionTime = (endTime - startTime).TotalSeconds;

            jobResult.EndTime = endTime;
            jobResult.ExecutionTimeSeconds = (decimal)executionTime;

            if (!completed)
            {
                // タイムアウト
                _logService.Warning($"ジョブがタイムアウトしました ({job.LimitTime}秒)");

                try
                {
                    // endFlg に応じた処理
                    if (job.EndFlg == 3)
                    {
                        // 強制終了
                        process.Kill(entireProcessTree: true);
                        _logService.Information("プロセスを強制終了しました");
                    }

                    jobResult.Status = "Timeout";
                    jobResult.ErrorMessage = $"タイムアウト（制限時間: {job.LimitTime}秒）";
                }
                catch (Exception killEx)
                {
                    _logService.Error(killEx, "プロセスの強制終了に失敗しました");
                }
            }
            else
            {
                // 正常終了またはエラー終了
                jobResult.ExitCode = process.ExitCode;

                if (process.ExitCode == 0)
                {
                    jobResult.Status = "Success";
                    _logService.Information($"ジョブが正常終了しました (ExitCode: 0, 実行時間: {executionTime:F2}秒)");
                }
                else
                {
                    jobResult.Status = "Failed";
                    jobResult.ErrorMessage = $"ExitCode: {process.ExitCode}";

                    if (errorBuilder.Length > 0)
                    {
                        jobResult.ErrorMessage += $"\n{errorBuilder.ToString().Trim()}";
                    }

                    _logService.Error($"ジョブがエラー終了しました (ExitCode: {process.ExitCode})");
                }
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ジョブ実行中に例外が発生しました");

            jobResult.EndTime = DateTime.Now;
            jobResult.ExecutionTimeSeconds = (decimal)(jobResult.EndTime.Value - startTime).TotalSeconds;
            jobResult.Status = "Failed";
            jobResult.ErrorMessage = ex.Message;
            jobResult.ExitCode = -1;
        }

        return jobResult;
    }
}

