namespace BatchManager.Core.Interfaces;

/// <summary>
/// メール送信を行うサービスのインターフェース
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// メールを送信する
    /// </summary>
    /// <param name="subject">件名</param>
    /// <param name="body">本文</param>
    /// <param name="isHtml">HTMLメールかどうか（デフォルト: false）</param>
    Task SendEmailAsync(string subject, string body, bool isHtml = false);

    /// <summary>
    /// タイムアウト通知メールを送信する
    /// </summary>
    /// <param name="jobName">タイムアウトしたジョブ名</param>
    /// <param name="timeoutSeconds">タイムアウト設定（秒）</param>
    /// <param name="instanceName">インスタンス名</param>
    Task SendTimeoutNotificationAsync(string jobName, int timeoutSeconds, string instanceName);

    /// <summary>
    /// エラー通知メールを送信する
    /// </summary>
    /// <param name="jobName">エラーが発生したジョブ名</param>
    /// <param name="errorMessage">エラーメッセージ</param>
    /// <param name="instanceName">インスタンス名</param>
    Task SendErrorNotificationAsync(string jobName, string errorMessage, string instanceName);

    /// <summary>
    /// バッチ完了通知メールを送信する
    /// </summary>
    /// <param name="instanceName">インスタンス名</param>
    /// <param name="totalJobs">総ジョブ数</param>
    /// <param name="successJobs">成功ジョブ数</param>
    /// <param name="timeoutJobs">タイムアウトジョブ数</param>
    /// <param name="errorJobs">エラージョブ数</param>
    /// <param name="duration">実行時間</param>
    Task SendCompletionNotificationAsync(
        string instanceName,
        int totalJobs,
        int successJobs,
        int timeoutJobs,
        int errorJobs,
        TimeSpan duration);
}



