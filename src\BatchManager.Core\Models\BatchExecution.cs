namespace BatchManager.Core.Models;

/// <summary>
/// バッチ実行履歴を表すモデル（データベースのbatch_executionsテーブルに対応）
/// </summary>
public class BatchExecution
{
    /// <summary>
    /// バッチ実行ID（自動採番）
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// インスタンス名（BatchManagerNet.exe.configのInstanceName）
    /// </summary>
    public string InstanceName { get; set; } = string.Empty;

    /// <summary>
    /// 顧客名（BatchManagerNet.exe.configのCustomerName）
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// バッチの説明（BatchManagerNet.exe.configのBatchDescription）
    /// </summary>
    public string? BatchDescription { get; set; }

    /// <summary>
    /// バッチ実行開始日時（UTC）
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// バッチ実行終了日時（UTC）
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// バッチ全体のステータス（"Success", "Warning", "Error"）
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 実行されたジョブの総数
    /// </summary>
    public int TotalJobs { get; set; }

    /// <summary>
    /// 成功したジョブ数
    /// </summary>
    public int SuccessfulJobs { get; set; }

    /// <summary>
    /// タイムアウトしたジョブ数
    /// </summary>
    public int TimeoutJobs { get; set; }

    /// <summary>
    /// エラーが発生したジョブ数
    /// </summary>
    public int FailedJobs { get; set; }

    /// <summary>
    /// バッチ全体の実行時間（秒）
    /// </summary>
    public decimal? TotalDurationSeconds { get; set; }

    /// <summary>
    /// バッチ実行の終了コード
    /// </summary>
    public int ExitCode { get; set; }

    /// <summary>
    /// メール通知が送信されたか
    /// </summary>
    public bool EmailSent { get; set; }

    /// <summary>
    /// エラーメッセージ（あれば）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// レコード作成日時（UTC）
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 関連するジョブ実行履歴のリスト
    /// </summary>
    public List<JobExecution> JobExecutions { get; set; } = new();

    /// <summary>
    /// バッチ実行の経過時間（計算プロパティ）
    /// </summary>
    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
}



