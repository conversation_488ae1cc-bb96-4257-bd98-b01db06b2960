{"@t":"2025-10-01T03:57:12.0586471Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T03:57:12.0820759Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T03:57:12.0831777Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T03:57:12.0835658Z","@mt":"アプリケーション設定を読み込みました: DefaultInstance"}
{"@t":"2025-10-01T03:57:12.0842304Z","@mt":"インスタンス名: DefaultInstance"}
{"@t":"2025-10-01T03:57:12.0845544Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T03:57:12.0910337Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T03:57:12.0938494Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T03:57:12.0943414Z","@mt":"設定ファイルが見つかりません: BatchManagerNet.config","@l":"Error"}
{"@t":"2025-10-01T03:57:12.0960730Z","@mt":"設定ファイルの読み込みに失敗しました: BatchManagerNet.config","@l":"Error","@x":"System.IO.FileNotFoundException: 設定ファイルが見つかりません\r\nFile name: 'BatchManagerNet.config'\r\n   at BatchManager.Core.Services.ConfigService.LoadJobConfigAsync(String configFilePath) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\ConfigService.cs:line 31"}
{"@t":"2025-10-01T03:57:12.1289235Z","@mt":"バッチ実行中に致命的なエラーが発生しました","@l":"Error","@x":"System.IO.FileNotFoundException: 設定ファイルが見つかりません\r\nFile name: 'BatchManagerNet.config'\r\n   at BatchManager.Core.Services.ConfigService.LoadJobConfigAsync(String configFilePath) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\ConfigService.cs:line 31\r\n   at BatchManager.Executor.Services.BatchRunner.RunAsync() in C:\\projects\\BatchManager\\src\\BatchManager.Executor\\Services\\BatchRunner.cs:line 53"}
{"@t":"2025-10-01T03:57:12.1325918Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
2025-10-01 16:44:45.299 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 16:44:45.316 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 16:44:45.318 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 16:44:45.318 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 16:44:45.319 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 16:44:45.319 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 16:44:45.325 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 16:44:45.327 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 16:44:45.380 +09:00 [INF] ジョブ設定を読み込みました: 5件
2025-10-01 16:44:45.381 +09:00 [INF] ジョブ数: 5
2025-10-01 16:44:45.382 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 16:44:45.383 +09:00 [INF] [1/5] ジョブ実行開始: D:\NewWebReportBatch\YS_I24_SupplierMasterDiffUpdate\YS_I24_SupplierMasterDiffUpdate.exe
2025-10-01 16:44:45.389 +09:00 [ERR] ジョブ実行中に例外が発生しました
System.ComponentModel.Win32Exception (2): An error occurred trying to start process 'D:\NewWebReportBatch\YS_I24_SupplierMasterDiffUpdate\YS_I24_SupplierMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)
   at BatchManager.Executor.Services.JobExecutor.ExecuteJobAsync(ComList job, Int32 jobSequence) in C:\projects\BatchManager\src\BatchManager.Executor\Services\JobExecutor.cs:line 76
2025-10-01 16:44:45.412 +09:00 [ERR] ジョブ実行エラー: An error occurred trying to start process 'D:\NewWebReportBatch\YS_I24_SupplierMasterDiffUpdate\YS_I24_SupplierMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
2025-10-01 16:44:45.413 +09:00 [INF] [2/5] ジョブはスキップされました（無効）: D:\NewWebReportBatch\YS_I23_StoreMasterDiffUpdate\YS_I23_StoreMasterDiffUpdate.exe
2025-10-01 16:44:45.413 +09:00 [INF] [3/5] ジョブ実行開始: D:\NewWebReportBatch\YS_I20_ItemMasterDiffUpdate\YS_I20_ItemMasterDiffUpdate.exe
2025-10-01 16:44:45.414 +09:00 [ERR] ジョブ実行中に例外が発生しました
System.ComponentModel.Win32Exception (2): An error occurred trying to start process 'D:\NewWebReportBatch\YS_I20_ItemMasterDiffUpdate\YS_I20_ItemMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)
   at BatchManager.Executor.Services.JobExecutor.ExecuteJobAsync(ComList job, Int32 jobSequence) in C:\projects\BatchManager\src\BatchManager.Executor\Services\JobExecutor.cs:line 76
2025-10-01 16:44:45.415 +09:00 [ERR] ジョブ実行エラー: An error occurred trying to start process 'D:\NewWebReportBatch\YS_I20_ItemMasterDiffUpdate\YS_I20_ItemMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
2025-10-01 16:44:45.416 +09:00 [INF] [4/5] ジョブ実行開始: D:\NewWebReportBatch\YS_I21_PluMaterDiffUpdate\YS_I21_PluMaterDiffUpdate.exe
2025-10-01 16:44:45.419 +09:00 [ERR] ジョブ実行中に例外が発生しました
System.ComponentModel.Win32Exception (2): An error occurred trying to start process 'D:\NewWebReportBatch\YS_I21_PluMaterDiffUpdate\YS_I21_PluMaterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)
   at BatchManager.Executor.Services.JobExecutor.ExecuteJobAsync(ComList job, Int32 jobSequence) in C:\projects\BatchManager\src\BatchManager.Executor\Services\JobExecutor.cs:line 76
2025-10-01 16:44:45.420 +09:00 [ERR] ジョブ実行エラー: An error occurred trying to start process 'D:\NewWebReportBatch\YS_I21_PluMaterDiffUpdate\YS_I21_PluMaterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
2025-10-01 16:44:45.420 +09:00 [INF] [5/5] ジョブ実行開始: D:\NewWebReportBatch\YS_I22_StoreItemMasterDiffUpdate\YS_I22_StoreItemMasterDiffUpdate.exe
2025-10-01 16:44:45.421 +09:00 [ERR] ジョブ実行中に例外が発生しました
System.ComponentModel.Win32Exception (2): An error occurred trying to start process 'D:\NewWebReportBatch\YS_I22_StoreItemMasterDiffUpdate\YS_I22_StoreItemMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)
   at BatchManager.Executor.Services.JobExecutor.ExecuteJobAsync(ComList job, Int32 jobSequence) in C:\projects\BatchManager\src\BatchManager.Executor\Services\JobExecutor.cs:line 76
2025-10-01 16:44:45.421 +09:00 [ERR] ジョブ実行エラー: An error occurred trying to start process 'D:\NewWebReportBatch\YS_I22_StoreItemMasterDiffUpdate\YS_I22_StoreItemMasterDiffUpdate.exe' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
2025-10-01 16:44:45.422 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 16:44:45.423 +09:00 [INF] バッチ実行完了:
2025-10-01 16:44:45.423 +09:00 [INF]   総ジョブ数: 5
2025-10-01 16:44:45.423 +09:00 [INF]   成功: 0
2025-10-01 16:44:45.424 +09:00 [INF]   タイムアウト: 0
2025-10-01 16:44:45.424 +09:00 [INF]   失敗: 4
2025-10-01 16:44:45.426 +09:00 [INF]   実行時間: 0.10秒
2025-10-01 16:44:45.426 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:02:12.654 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:02:12.678 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:02:12.680 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:02:12.680 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:02:12.681 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:02:12.682 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:02:12.692 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:02:12.696 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:02:12.721 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:02:12.722 +09:00 [INF] ジョブ数: 4
2025-10-01 17:02:12.723 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:02:12.724 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:02:17.167 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.44秒)
2025-10-01 17:02:17.171 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:02:17.172 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:02:22.154 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.98秒)
2025-10-01 17:02:22.160 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:02:22.161 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:02:27.181 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.02秒)
2025-10-01 17:02:27.186 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:02:27.187 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:02:32.188 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.00秒)
2025-10-01 17:02:32.190 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:02:32.192 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:02:32.196 +09:00 [INF] バッチ実行完了:
2025-10-01 17:02:32.197 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:02:32.198 +09:00 [INF]   成功: 4
2025-10-01 17:02:32.199 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:02:32.200 +09:00 [INF]   失敗: 0
2025-10-01 17:02:32.202 +09:00 [INF]   実行時間: 19.50秒
2025-10-01 17:02:32.202 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:10:47.503 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:10:47.524 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:10:47.525 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:10:47.525 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:10:47.526 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:10:47.526 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:10:47.532 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:10:47.540 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:10:47.564 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:10:47.564 +09:00 [INF] ジョブ数: 4
2025-10-01 17:10:47.586 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:10:47.587 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:10:52.173 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.58秒)
2025-10-01 17:10:52.178 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:10:52.180 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:10:57.142 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.96秒)
2025-10-01 17:10:57.148 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:10:57.149 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:11:02.228 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.08秒)
2025-10-01 17:11:02.237 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:11:02.238 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:11:07.248 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.01秒)
2025-10-01 17:11:07.271 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:11:07.272 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:11:07.273 +09:00 [INF] バッチ実行完了:
2025-10-01 17:11:07.274 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:11:07.275 +09:00 [INF]   成功: 4
2025-10-01 17:11:07.275 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:11:07.276 +09:00 [INF]   失敗: 0
2025-10-01 17:11:07.278 +09:00 [INF]   実行時間: 19.74秒
2025-10-01 17:11:07.278 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:11:07.280 +09:00 [INF] DB転送を非同期で開始しました
2025-10-01 17:11:07.282 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:11:07.283 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:16:09.416 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:16:09.433 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:16:09.434 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:16:09.435 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:16:09.437 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:16:09.437 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:16:09.442 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:16:09.444 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:16:09.469 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:16:09.471 +09:00 [INF] ジョブ数: 4
2025-10-01 17:16:09.474 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:16:09.475 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:16:14.221 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.74秒)
2025-10-01 17:16:14.224 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:16:14.225 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:16:19.217 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.99秒)
2025-10-01 17:16:19.224 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:16:19.225 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:16:24.157 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.93秒)
2025-10-01 17:16:24.161 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:16:24.163 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:16:29.185 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.02秒)
2025-10-01 17:16:29.198 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:16:29.199 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:16:29.202 +09:00 [INF] バッチ実行完了:
2025-10-01 17:16:29.207 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:16:29.207 +09:00 [INF]   成功: 4
2025-10-01 17:16:29.208 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:16:29.209 +09:00 [INF]   失敗: 0
2025-10-01 17:16:29.211 +09:00 [INF]   実行時間: 19.76秒
2025-10-01 17:16:29.212 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:16:29.213 +09:00 [INF] DB転送を非同期で開始しました
2025-10-01 17:16:29.214 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:19:43.185 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:19:43.203 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:19:43.203 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:19:43.203 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:19:43.203 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:19:43.203 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:19:43.209 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:19:43.210 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:19:43.236 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:19:43.236 +09:00 [INF] ジョブ数: 4
2025-10-01 17:19:43.238 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:19:43.238 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:19:43.353 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.360 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:19:43.361 +09:00 [ERR] ジョブ実行エラー: ExitCode: 1
エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.361 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:19:43.473 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.480 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:19:43.480 +09:00 [ERR] ジョブ実行エラー: ExitCode: 1
エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.480 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:19:43.554 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.560 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:19:43.560 +09:00 [ERR] ジョブ実行エラー: ExitCode: 1
エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.560 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:19:43.621 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.626 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:19:43.626 +09:00 [ERR] ジョブ実行エラー: ExitCode: 1
エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:19:43.626 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:19:43.626 +09:00 [INF] バッチ実行完了:
2025-10-01 17:19:43.627 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:19:43.628 +09:00 [INF]   成功: 0
2025-10-01 17:19:43.628 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:19:43.628 +09:00 [INF]   失敗: 4
2025-10-01 17:19:43.629 +09:00 [INF]   実行時間: 0.42秒
2025-10-01 17:19:43.629 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:19:43.631 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:19:45.198 +09:00 [INF] バッチ実行履歴を登録しました (ID: 1)
2025-10-01 17:19:45.452 +09:00 [ERR] ジョブ実行履歴の一括挿入に失敗しました
Npgsql.PostgresException (0x80004005): 23514: new row for relation "job_executions" violates check constraint "job_executions_status_check"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteMultiImplAsync(IDbConnection cnn, CommandDefinition command, IEnumerable multiExec) in /_/Dapper/SqlMapper.Async.cs:line 640
   at BatchManager.Core.Services.DatabaseService.InsertJobExecutionsBatchAsync(IEnumerable`1 jobExecutions) in C:\projects\BatchManager\src\BatchManager.Core\Services\DatabaseService.cs:line 323
   at BatchManager.Core.Services.DatabaseService.InsertJobExecutionsBatchAsync(IEnumerable`1 jobExecutions) in C:\projects\BatchManager\src\BatchManager.Core\Services\DatabaseService.cs:line 331
  Exception data:
    Severity: ERROR
    SqlState: 23514
    MessageText: new row for relation "job_executions" violates check constraint "job_executions_status_check"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: job_executions
    ConstraintName: job_executions_status_check
    File: execMain.c
    Line: 2049
    Routine: ExecConstraints
2025-10-01 17:19:45.507 +09:00 [ERR] データベースへのアップロードに失敗しました
Npgsql.PostgresException (0x80004005): 23514: new row for relation "job_executions" violates check constraint "job_executions_status_check"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteMultiImplAsync(IDbConnection cnn, CommandDefinition command, IEnumerable multiExec) in /_/Dapper/SqlMapper.Async.cs:line 640
   at BatchManager.Core.Services.DatabaseService.InsertJobExecutionsBatchAsync(IEnumerable`1 jobExecutions) in C:\projects\BatchManager\src\BatchManager.Core\Services\DatabaseService.cs:line 323
   at BatchManager.Core.Services.DatabaseService.InsertJobExecutionsBatchAsync(IEnumerable`1 jobExecutions) in C:\projects\BatchManager\src\BatchManager.Core\Services\DatabaseService.cs:line 331
   at BatchManager.Executor.Services.BatchRunner.UploadToDatabaseAsync(BatchExecution batchResult) in C:\projects\BatchManager\src\BatchManager.Executor\Services\BatchRunner.cs:line 217
  Exception data:
    Severity: ERROR
    SqlState: 23514
    MessageText: new row for relation "job_executions" violates check constraint "job_executions_status_check"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: job_executions
    ConstraintName: job_executions_status_check
    File: execMain.c
    Line: 2049
    Routine: ExecConstraints
2025-10-01 17:19:45.590 +09:00 [INF] アップロード失敗データを保存しました: UploadQueue\batch_20251001081943_7306fb5b-3d0e-4b7b-a2fc-dfd569d89377.json
2025-10-01 17:19:45.590 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:21:12.163 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:21:12.230 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:21:12.231 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:21:12.231 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:21:12.231 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:21:12.231 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:21:12.237 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:21:12.239 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:21:12.286 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:21:12.286 +09:00 [INF] ジョブ数: 4
2025-10-01 17:21:12.288 +09:00 [INF] 前回失敗したアップロードを再送信します (1件)
2025-10-01 17:21:12.339 +09:00 [INF] 再送信中: batch_20251001081943_7306fb5b-3d0e-4b7b-a2fc-dfd569d89377.json
2025-10-01 17:21:12.340 +09:00 [INF] 再送信成功: batch_20251001081943_7306fb5b-3d0e-4b7b-a2fc-dfd569d89377.json
2025-10-01 17:21:12.340 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:21:12.340 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:21:12.491 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:21:12.497 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:21:12.498 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:21:12.498 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:21:12.580 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:21:12.586 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:21:12.587 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:21:12.587 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:21:12.667 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:21:12.675 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:21:12.675 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:21:12.675 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:21:12.757 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:21:12.765 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:21:12.766 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:21:12.766 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:21:12.767 +09:00 [INF] バッチ実行完了:
2025-10-01 17:21:12.767 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:21:12.767 +09:00 [INF]   成功: 0
2025-10-01 17:21:12.767 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:21:12.767 +09:00 [INF]   失敗: 0
2025-10-01 17:21:12.769 +09:00 [INF]   実行時間: 0.53秒
2025-10-01 17:21:12.769 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:21:12.770 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:21:14.116 +09:00 [INF] バッチ実行履歴を登録しました (ID: 2)
2025-10-01 17:21:14.743 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 4件
2025-10-01 17:21:14.744 +09:00 [INF] ジョブ実行履歴を登録しました (4件)
2025-10-01 17:21:14.745 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:21:14.745 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:22:05.469 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:22:05.491 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:22:05.491 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:22:05.492 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:22:05.493 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:22:05.493 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:22:05.498 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:22:05.499 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:22:05.521 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:22:05.523 +09:00 [INF] ジョブ数: 4
2025-10-01 17:22:05.527 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:22:05.529 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:22:10.226 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.69秒)
2025-10-01 17:22:10.229 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:22:10.231 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:22:15.124 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.89秒)
2025-10-01 17:22:15.130 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:22:15.132 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:22:20.137 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.00秒)
2025-10-01 17:22:20.144 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:22:20.149 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:22:25.170 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.02秒)
2025-10-01 17:22:25.175 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:22:25.177 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:22:25.180 +09:00 [INF] バッチ実行完了:
2025-10-01 17:22:25.181 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:22:25.182 +09:00 [INF]   成功: 4
2025-10-01 17:22:25.183 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:22:25.184 +09:00 [INF]   失敗: 0
2025-10-01 17:22:25.186 +09:00 [INF]   実行時間: 19.68秒
2025-10-01 17:22:25.187 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:22:25.189 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:22:26.624 +09:00 [INF] バッチ実行履歴を登録しました (ID: 3)
2025-10-01 17:22:27.264 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 4件
2025-10-01 17:22:27.267 +09:00 [INF] ジョブ実行履歴を登録しました (4件)
2025-10-01 17:22:27.269 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:22:27.271 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:25:53.225 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:25:53.242 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:25:53.242 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:25:53.242 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:25:53.243 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:25:53.243 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:25:53.248 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:25:53.249 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:25:53.274 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:25:53.275 +09:00 [INF] ジョブ数: 4
2025-10-01 17:25:53.277 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:25:53.277 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:25:53.366 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:25:53.374 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:25:53.374 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:25:53.374 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:25:53.455 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:25:53.461 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:25:53.462 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:25:53.462 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:25:53.536 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:25:53.540 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:25:53.540 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:25:53.540 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat4.bat
2025-10-01 17:25:53.609 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:25:53.616 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:25:53.616 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:25:53.616 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:25:53.616 +09:00 [INF] バッチ実行完了:
2025-10-01 17:25:53.616 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:25:53.616 +09:00 [INF]   成功: 0
2025-10-01 17:25:53.616 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:25:53.617 +09:00 [INF]   失敗: 0
2025-10-01 17:25:53.618 +09:00 [INF]   実行時間: 0.37秒
2025-10-01 17:25:53.618 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:25:53.618 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:25:55.002 +09:00 [INF] バッチ実行履歴を登録しました (ID: 4)
2025-10-01 17:25:55.641 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 4件
2025-10-01 17:25:55.642 +09:00 [INF] ジョブ実行履歴を登録しました (4件)
2025-10-01 17:25:55.642 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:25:55.642 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:28:07.713 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:28:07.730 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:28:07.730 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:28:07.731 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:28:07.732 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:28:07.732 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:28:07.737 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:28:07.739 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:28:07.767 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:28:07.767 +09:00 [INF] ジョブ数: 4
2025-10-01 17:28:07.771 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:28:07.771 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:28:12.235 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.46秒)
2025-10-01 17:28:12.238 +09:00 [INF] [1/4] ジョブ実行完了: 成功
2025-10-01 17:28:12.239 +09:00 [INF] [2/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:28:17.126 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 4.89秒)
2025-10-01 17:28:17.133 +09:00 [INF] [2/4] ジョブ実行完了: 成功
2025-10-01 17:28:17.134 +09:00 [INF] [3/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:28:22.143 +09:00 [INF] ジョブが正常終了しました (ExitCode: 0, 実行時間: 5.01秒)
2025-10-01 17:28:22.148 +09:00 [INF] [3/4] ジョブ実行完了: 成功
2025-10-01 17:28:22.150 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:28:22.152 +09:00 [ERR] ジョブ実行中に例外が発生しました
System.ComponentModel.Win32Exception (2): An error occurred trying to start process 'C:\projects\BatchManager\testbatch\bat5.bat' with working directory 'C:\projects\BatchManager\src\BatchManager.Executor\bin\Debug\net8.0'. 指定されたファイルが見つかりません。
   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)
   at BatchManager.Executor.Services.JobExecutor.ExecuteJobAsync(ComList job, Int32 jobSequence) in C:\projects\BatchManager\src\BatchManager.Executor\Services\JobExecutor.cs:line 76
2025-10-01 17:28:22.182 +09:00 [INF] [4/4] ジョブ実行完了: 成功
2025-10-01 17:28:22.183 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:28:22.184 +09:00 [INF] バッチ実行完了:
2025-10-01 17:28:22.185 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:28:22.186 +09:00 [INF]   成功: 3
2025-10-01 17:28:22.186 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:28:22.188 +09:00 [INF]   失敗: 0
2025-10-01 17:28:22.190 +09:00 [INF]   実行時間: 14.45秒
2025-10-01 17:28:22.190 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:28:22.192 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:28:23.816 +09:00 [INF] バッチ実行履歴を登録しました (ID: 5)
2025-10-01 17:28:24.458 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 4件
2025-10-01 17:28:24.461 +09:00 [INF] ジョブ実行履歴を登録しました (4件)
2025-10-01 17:28:24.462 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:28:24.463 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 0) =====
2025-10-01 17:31:38.073 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:31:38.093 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:31:38.093 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:31:38.093 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:31:38.093 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:31:38.093 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:31:38.098 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:31:38.099 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:31:38.123 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:31:38.124 +09:00 [INF] ジョブ数: 4
2025-10-01 17:31:38.126 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:31:38.126 +09:00 [INF] [1/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:31:38.233 +09:00 [WRN] [STDERR] エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:31:38.243 +09:00 [ERR] ジョブがエラー終了しました (ExitCode: 1)
2025-10-01 17:31:38.243 +09:00 [ERR] ジョブ実行エラー: ExitCode: 1
エラー: 入力のリダイレクトはサポートされていません。今すぐプロセスを終了します。
2025-10-01 17:31:38.324 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat1
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:31:38.340 +09:00 [ERR] バッチ実行中に致命的なエラーが発生しました
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
   at BatchManager.Core.Services.EmailService.SendErrorNotificationAsync(String jobName, String errorMessage, String instanceName) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 133
   at BatchManager.Executor.Services.BatchRunner.RunAsync() in C:\projects\BatchManager\src\BatchManager.Executor\Services\BatchRunner.cs:line 108
2025-10-01 17:31:38.341 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:32:55.787 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:32:55.807 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:32:55.807 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:32:55.807 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:32:55.807 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:32:55.807 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:32:55.814 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:32:55.815 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:32:55.841 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:32:55.841 +09:00 [INF] ジョブ数: 4
2025-10-01 17:32:55.844 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:32:55.844 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:32:55.844 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:32:55.844 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:32:55.844 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:32:55.846 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:32:55.847 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:32:55.896 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:32:55.909 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:32:55.909 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:32:55.910 +09:00 [INF] バッチ実行完了:
2025-10-01 17:32:55.910 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:32:55.910 +09:00 [INF]   成功: 0
2025-10-01 17:32:55.910 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:32:55.910 +09:00 [INF]   失敗: 0
2025-10-01 17:32:55.911 +09:00 [INF]   実行時間: 0.10秒
2025-10-01 17:32:55.911 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:32:55.912 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:32:57.281 +09:00 [INF] バッチ実行履歴を登録しました (ID: 6)
2025-10-01 17:32:57.548 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:32:57.549 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:32:57.549 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:32:57.549 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:33:38.062 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:33:38.078 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:33:38.079 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:33:38.080 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:33:38.081 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:33:38.081 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:33:38.086 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:33:38.088 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:33:38.110 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:33:38.111 +09:00 [INF] ジョブ数: 4
2025-10-01 17:33:38.115 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:33:38.116 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:33:38.116 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:33:38.117 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:33:38.117 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:38.119 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:38.120 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:38.167 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:33:38.182 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:33:38.183 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:33:38.183 +09:00 [INF] バッチ実行完了:
2025-10-01 17:33:38.184 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:33:38.184 +09:00 [INF]   成功: 0
2025-10-01 17:33:38.184 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:33:38.185 +09:00 [INF]   失敗: 0
2025-10-01 17:33:38.186 +09:00 [INF]   実行時間: 0.10秒
2025-10-01 17:33:38.187 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:33:38.188 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:33:39.751 +09:00 [INF] バッチ実行履歴を登録しました (ID: 7)
2025-10-01 17:33:40.016 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:33:40.019 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:33:40.021 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:33:40.022 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:33:44.374 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:33:44.401 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:33:44.403 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:33:44.403 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:33:44.404 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:33:44.404 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:33:44.412 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:33:44.414 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:33:44.436 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:33:44.438 +09:00 [INF] ジョブ数: 4
2025-10-01 17:33:44.440 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:33:44.440 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:33:44.441 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:33:44.442 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:33:44.442 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:44.444 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:44.446 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:33:44.498 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:33:44.522 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:33:44.522 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:33:44.523 +09:00 [INF] バッチ実行完了:
2025-10-01 17:33:44.524 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:33:44.524 +09:00 [INF]   成功: 0
2025-10-01 17:33:44.524 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:33:44.525 +09:00 [INF]   失敗: 0
2025-10-01 17:33:44.526 +09:00 [INF]   実行時間: 0.11秒
2025-10-01 17:33:44.527 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:33:44.528 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:33:45.817 +09:00 [INF] バッチ実行履歴を登録しました (ID: 8)
2025-10-01 17:33:46.056 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:33:46.061 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:33:46.062 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:33:46.072 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:37:52.536 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:37:52.559 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:37:52.559 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:37:52.560 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:37:52.561 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:37:52.561 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:37:52.568 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:37:52.569 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:37:52.595 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:37:52.597 +09:00 [INF] ジョブ数: 4
2025-10-01 17:37:52.600 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:37:52.600 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:37:52.601 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:37:52.601 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:37:52.602 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:37:52.603 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:37:52.604 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:37:52.645 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:37:52.659 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:37:52.660 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:37:52.660 +09:00 [INF] バッチ実行完了:
2025-10-01 17:37:52.661 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:37:52.661 +09:00 [INF]   成功: 0
2025-10-01 17:37:52.662 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:37:52.662 +09:00 [INF]   失敗: 0
2025-10-01 17:37:52.665 +09:00 [INF]   実行時間: 0.09秒
2025-10-01 17:37:52.665 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:37:52.667 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:37:54.731 +09:00 [INF] バッチ実行履歴を登録しました (ID: 9)
2025-10-01 17:37:55.088 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:37:55.092 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:37:55.093 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:37:55.094 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:40:08.473 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:40:08.489 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:40:08.489 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:40:08.489 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:40:08.490 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:40:08.490 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:40:08.496 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:40:08.496 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:40:08.520 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:40:08.521 +09:00 [INF] ジョブ数: 4
2025-10-01 17:40:08.523 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:40:08.523 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:40:08.524 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:40:08.524 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:40:08.524 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:40:08.525 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:40:08.526 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:40:08.573 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:40:08.584 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:40:08.584 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:40:08.585 +09:00 [INF] バッチ実行完了:
2025-10-01 17:40:08.585 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:40:08.585 +09:00 [INF]   成功: 0
2025-10-01 17:40:08.585 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:40:08.585 +09:00 [INF]   失敗: 0
2025-10-01 17:40:08.586 +09:00 [INF]   実行時間: 0.09秒
2025-10-01 17:40:08.586 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:40:08.586 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:40:10.416 +09:00 [INF] バッチ実行履歴を登録しました (ID: 10)
2025-10-01 17:40:10.757 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:40:10.759 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:40:10.759 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:40:10.760 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:41:02.553 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:41:02.577 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:41:02.577 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:41:02.577 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:41:02.578 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:41:02.578 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:41:02.585 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:41:02.587 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:41:02.617 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:41:02.617 +09:00 [INF] ジョブ数: 4
2025-10-01 17:41:02.621 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:41:02.621 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:41:02.621 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:41:02.622 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:41:02.622 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:41:02.627 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:41:02.628 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:41:02.633 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:41:02.633 +09:00 [INF] バッチ実行完了:
2025-10-01 17:41:02.633 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:41:02.633 +09:00 [INF]   成功: 0
2025-10-01 17:41:02.633 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:41:02.633 +09:00 [INF]   失敗: 0
2025-10-01 17:41:02.635 +09:00 [INF]   実行時間: 0.05秒
2025-10-01 17:41:02.635 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:41:02.636 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:41:04.418 +09:00 [INF] バッチ実行履歴を登録しました (ID: 11)
2025-10-01 17:41:04.784 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:41:04.784 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:41:04.784 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:41:04.784 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:44:03.780 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:44:03.798 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:44:03.800 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:44:03.801 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:44:03.802 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:44:03.803 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:44:03.810 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:44:03.813 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:44:03.845 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:44:03.847 +09:00 [INF] ジョブ数: 4
2025-10-01 17:44:03.851 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:44:03.852 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:44:03.853 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:44:03.854 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:44:03.854 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:44:03.857 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:44:03.858 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:44:03.909 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:44:03.953 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:44:03.954 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:44:03.954 +09:00 [INF] バッチ実行完了:
2025-10-01 17:44:03.955 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:44:03.956 +09:00 [INF]   成功: 0
2025-10-01 17:44:03.956 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:44:03.956 +09:00 [INF]   失敗: 0
2025-10-01 17:44:03.958 +09:00 [INF]   実行時間: 0.14秒
2025-10-01 17:44:03.958 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:44:03.959 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:44:05.847 +09:00 [INF] バッチ実行履歴を登録しました (ID: 12)
2025-10-01 17:44:06.207 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:44:06.209 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:44:06.210 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:44:06.211 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
2025-10-01 17:46:07.180 +09:00 [INF] ログサービスを初期化しました: BatchManagerNet.log
2025-10-01 17:46:07.206 +09:00 [INF] ===== BatchManagerNet 起動 =====
2025-10-01 17:46:07.206 +09:00 [INF] アプリケーション設定を読み込んでいます
2025-10-01 17:46:07.207 +09:00 [INF] アプリケーション設定を読み込みました: BatchManagerNet
2025-10-01 17:46:07.207 +09:00 [INF] メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''
2025-10-01 17:46:07.207 +09:00 [INF] インスタンス名: BatchManagerNet
2025-10-01 17:46:07.207 +09:00 [INF] 設定ファイル: BatchManagerNet.config
2025-10-01 17:46:07.218 +09:00 [INF] ===== フェーズ1: 初期化 =====
2025-10-01 17:46:07.220 +09:00 [INF] ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config
2025-10-01 17:46:07.250 +09:00 [INF] ジョブ設定を読み込みました: 4件
2025-10-01 17:46:07.251 +09:00 [INF] ジョブ数: 4
2025-10-01 17:46:07.253 +09:00 [INF] ===== フェーズ2: ジョブ実行 =====
2025-10-01 17:46:07.253 +09:00 [INF] [1/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat1.bat
2025-10-01 17:46:07.253 +09:00 [INF] [2/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat2.bat
2025-10-01 17:46:07.253 +09:00 [INF] [3/4] ジョブはスキップされました（無効）: C:\projects\BatchManager\testbatch\bat3.bat
2025-10-01 17:46:07.253 +09:00 [INF] [4/4] ジョブ実行開始: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:46:07.254 +09:00 [ERR] 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:46:07.255 +09:00 [ERR] ジョブ実行エラー: 実行ファイルが見つかりません: C:\projects\BatchManager\testbatch\bat5.bat
2025-10-01 17:46:07.304 +09:00 [ERR] メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5
System.ArgumentException: The host name cannot be empty. (Parameter 'host')
   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)
   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)
   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\projects\BatchManager\src\BatchManager.Core\Services\EmailService.cs:line 73
2025-10-01 17:46:07.316 +09:00 [WRN] エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')
2025-10-01 17:46:07.316 +09:00 [INF] ===== フェーズ3: 完了処理 =====
2025-10-01 17:46:07.318 +09:00 [INF] バッチ実行完了:
2025-10-01 17:46:07.318 +09:00 [INF]   総ジョブ数: 4
2025-10-01 17:46:07.318 +09:00 [INF]   成功: 0
2025-10-01 17:46:07.318 +09:00 [INF]   タイムアウト: 0
2025-10-01 17:46:07.318 +09:00 [INF]   失敗: 0
2025-10-01 17:46:07.319 +09:00 [INF]   実行時間: 0.10秒
2025-10-01 17:46:07.319 +09:00 [INF] ===== フェーズ4: DB転送 =====
2025-10-01 17:46:07.319 +09:00 [INF] データベースへのアップロードを開始します...
2025-10-01 17:46:09.083 +09:00 [INF] バッチ実行履歴を登録しました (ID: 13)
2025-10-01 17:46:09.434 +09:00 [INF] ジョブ実行履歴を一括挿入しました: 1件
2025-10-01 17:46:09.435 +09:00 [INF] ジョブ実行履歴を登録しました (1件)
2025-10-01 17:46:09.435 +09:00 [INF] データベースへのアップロード完了
2025-10-01 17:46:09.435 +09:00 [INF] ===== BatchManagerNet 終了 (ExitCode: 1) =====
{"@t":"2025-10-01T09:01:43.4592953Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:01:43.4882512Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:01:43.4894092Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:01:43.4895665Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:01:43.4895985Z","@mt":"メール設定: EmailEnabled=False, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:01:43.4901460Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:01:43.4902117Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:01:43.4959774Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:01:43.4970735Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:01:43.5269029Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:01:43.5271380Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:01:43.5284017Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:01:43.5285046Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:01:43.5285417Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:01:43.5285554Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:01:43.5285725Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:01:43.5299818Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:01:43.5307403Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:01:43.5342226Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:01:43.5345611Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:01:43.5346279Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:01:43.5346740Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:01:43.5349823Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:01:43.5350177Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:01:43.5360674Z","@mt":"  実行時間: 0.04秒"}
{"@t":"2025-10-01T09:01:43.5361183Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:01:43.5369702Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:01:45.8642261Z","@mt":"バッチ実行履歴を登録しました (ID: 14)"}
{"@t":"2025-10-01T09:01:46.2098635Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:01:46.2105934Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:01:46.2106713Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:01:46.2109848Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:04:29.3257954Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:04:29.3401995Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:04:29.3403701Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:04:29.3404669Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:04:29.3405030Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:04:29.3408174Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:04:29.3408775Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:04:29.3466557Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:04:29.3479421Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:04:29.3730700Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:04:29.3734449Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:04:29.3757464Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:04:29.3759529Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:04:29.3760012Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:04:29.3760175Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:04:29.3760393Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:04:29.3778153Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:04:29.3790595Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:04:29.4238612Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"System.ArgumentException: The host name cannot be empty. (Parameter 'host')\r\n   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)\r\n   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 73"}
{"@t":"2025-10-01T09:04:29.4357243Z","@mt":"エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')","@l":"Warning"}
{"@t":"2025-10-01T09:04:29.4358434Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:04:29.4361427Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:04:29.4362163Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:04:29.4362523Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:04:29.4362818Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:04:29.4363701Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:04:29.4374994Z","@mt":"  実行時間: 0.09秒"}
{"@t":"2025-10-01T09:04:29.4375817Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:04:29.4382711Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:04:33.6085966Z","@mt":"バッチ実行履歴を登録しました (ID: 15)"}
{"@t":"2025-10-01T09:04:33.9889286Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:04:33.9895012Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:04:33.9895750Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:04:33.9897334Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:41:33.7332849Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:41:33.7468757Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:41:33.7469949Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:41:33.7470364Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:41:33.7470507Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:41:33.7470627Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:41:33.7470744Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:41:33.7470868Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:41:33.7470977Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.7471080Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:41:33.7471248Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:41:33.7471365Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:41:33.7471480Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:41:33.7471602Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.7471745Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:41:33.7471847Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:41:33.7471945Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:41:33.7472178Z","@mt":"設定値解決: FromMailAddress Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7472320Z","@mt":"設定値解決: ToMailAddress Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7472429Z","@mt":"設定値解決: SmtpClient Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7472531Z","@mt":"設定値解決: User Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7472630Z","@mt":"設定値解決: Pass Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7472735Z","@mt":"設定値解決: EnableSsl Json='<null>' AppConfig='<null>' Result='true'"}
{"@t":"2025-10-01T09:41:33.7472882Z","@mt":"設定値解決: Port Json='<null>' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:41:33.7473876Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:41:33.7474155Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:41:33.7474299Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:41:33.7474509Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:41:33.7474688Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.7474801Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:41:33.7474958Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:41:33.7476309Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:41:33.7476520Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:41:33.7476651Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:41:33.7476761Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:41:33.7476862Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:41:33.7476961Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:41:33.7477092Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:41:33.7477194Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:41:33.7477290Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:41:33.7477377Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:41:33.7477553Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:41:33.7480463Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:41:33.7480753Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:41:33.7533430Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:41:33.7543604Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:41:33.7842840Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:41:33.7845105Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:41:33.7857230Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:41:33.7858480Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:41:33.7858790Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:41:33.7858910Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:41:33.7859072Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:41:33.7872954Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:41:33.7889701Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:41:33.8370250Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"System.ArgumentException: The host name cannot be empty. (Parameter 'host')\r\n   at MailKit.Net.Smtp.SmtpClient.ValidateArguments(String host, Int32 port)\r\n   at MailKit.Net.Smtp.SmtpClient.ConnectAsync(String host, Int32 port, SecureSocketOptions options, CancellationToken cancellationToken)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 73"}
{"@t":"2025-10-01T09:41:33.8523741Z","@mt":"エラーメール送信に失敗しました: The host name cannot be empty. (Parameter 'host')","@l":"Warning"}
{"@t":"2025-10-01T09:41:33.8524691Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:41:33.8527963Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:41:33.8528543Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:41:33.8528939Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:41:33.8529254Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:41:33.8529440Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:41:33.8538887Z","@mt":"  実行時間: 0.10秒"}
{"@t":"2025-10-01T09:41:33.8539522Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:41:33.8546057Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:41:36.3325505Z","@mt":"バッチ実行履歴を登録しました (ID: 16)"}
{"@t":"2025-10-01T09:41:36.6724939Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:41:36.6729450Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:41:36.6730085Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:41:36.6731295Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:43:07.8781603Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:43:07.8969650Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:43:07.8971438Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:43:07.8971871Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:43:07.8972106Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:43:07.8972260Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:43:07.8972413Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:43:07.8972547Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:43:07.8972791Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.8972982Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:43:07.8973266Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:43:07.8973526Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:43:07.8973843Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:43:07.8974049Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.8974918Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:43:07.8975188Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:43:07.8975420Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:43:07.8975759Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:43:07.8976378Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:43:07.8977762Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:43:07.8978306Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:43:07.8979070Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.8979831Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:43:07.8980531Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:43:07.8984381Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:43:07.8984973Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:43:07.8985999Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:43:07.8987126Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:43:07.8988082Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:43:07.8989030Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:43:07.8990233Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:43:07.8991316Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:43:07.8992309Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:43:07.8994873Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:43:07.8995922Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:43:07.9003447Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:43:07.9005035Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:43:07.9124837Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:43:07.9139107Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:43:07.9478351Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:43:07.9480822Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:43:07.9496109Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:43:07.9497682Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:43:07.9498104Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:43:07.9498892Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:43:07.9499170Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:43:07.9520573Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:43:07.9531824Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:43:08.0071680Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"MimeKit.ParseException: Unexpected token at offset 18\r\n   at MimeKit.MailboxAddress.Parse(ParserOptions options, String text)\r\n   at MimeKit.MailboxAddress.Parse(String text)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 47"}
{"@t":"2025-10-01T09:43:08.0251173Z","@mt":"エラーメール送信に失敗しました: Unexpected token at offset 18","@l":"Warning"}
{"@t":"2025-10-01T09:43:08.0252770Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:43:08.0257717Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:43:08.0258959Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:43:08.0259513Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:43:08.0259912Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:43:08.0260159Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:43:08.0276371Z","@mt":"  実行時間: 0.11秒"}
{"@t":"2025-10-01T09:43:08.0277203Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:43:08.0286805Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:43:09.8305791Z","@mt":"バッチ実行履歴を登録しました (ID: 17)"}
{"@t":"2025-10-01T09:43:10.1637937Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:43:10.1645536Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:43:10.1646710Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:43:10.1648693Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:44:01.6799232Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:44:01.6961480Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:44:01.6968469Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:44:01.6973725Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:44:01.6978097Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:44:01.6982848Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:44:01.6987750Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:44:01.6992177Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:44:01.6996681Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.7000951Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.7005057Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.7010014Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:44:01.7019230Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:44:01.7033224Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.7038685Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:44:01.7050838Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:44:01.7063438Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:44:01.7069791Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:44:01.7075510Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:44:01.7080363Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:44:01.7085180Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:44:01.7089690Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.7093846Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:44:01.7098219Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:44:01.7103319Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:44:01.7108231Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:44:01.7113428Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:44:01.7118559Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:44:01.7123231Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:44:01.7127760Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:44:01.7132449Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:44:01.7136857Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:44:01.7141716Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:44:01.7146743Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:44:01.7151288Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.7160224Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:44:01.7164961Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:44:01.7236001Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:44:01.7255451Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:44:01.7540652Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:44:01.7609728Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:44:01.7630483Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:44:01.7635330Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:44:01.7638884Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:44:01.7642056Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:44:01.7646238Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:44:01.7667331Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:44:01.7683718Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:44:01.8068743Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"MimeKit.ParseException: Unexpected token at offset 18\r\n   at MimeKit.MailboxAddress.Parse(ParserOptions options, String text)\r\n   at MimeKit.MailboxAddress.Parse(String text)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 47"}
{"@t":"2025-10-01T09:44:01.8205781Z","@mt":"エラーメール送信に失敗しました: Unexpected token at offset 18","@l":"Warning"}
{"@t":"2025-10-01T09:44:01.8211328Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:44:01.8221321Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:44:01.8228253Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:44:01.8233625Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:44:01.8238680Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:44:01.8242820Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:44:01.8259084Z","@mt":"  実行時間: 0.10秒"}
{"@t":"2025-10-01T09:44:01.8264599Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:44:01.8276639Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:44:03.6195305Z","@mt":"バッチ実行履歴を登録しました (ID: 18)"}
{"@t":"2025-10-01T09:44:03.9935159Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:44:03.9975860Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:44:03.9987471Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:44:04.0000393Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:45:25.8810138Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:45:25.8963612Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:45:25.8968832Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:45:25.8972456Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:45:25.8976117Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:45:25.8979585Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:45:25.8983091Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:45:25.8986763Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:45:25.8990310Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.8994443Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.8998245Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.9001810Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:45:25.9005397Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:45:25.9008921Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.9012579Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:45:25.9025595Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:45:25.9027763Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:45:25.9035086Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:45:25.9038866Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:45:25.9042590Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:45:25.9045722Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:45:25.9049523Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.9053058Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:45:25.9056800Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:45:25.9060720Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:45:25.9064266Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:45:25.9068010Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:45:25.9071607Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:45:25.9075341Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:45:25.9078832Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:45:25.9082298Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:45:25.9085777Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:45:25.9089164Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:45:25.9092574Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:45:25.9096102Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.9102624Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:45:25.9108477Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:45:25.9163394Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:45:25.9176528Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:45:25.9440695Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:45:25.9447302Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:45:25.9462087Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:45:25.9466998Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:45:25.9470857Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:45:25.9474341Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:45:25.9477959Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:45:25.9496218Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:45:25.9507577Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:45:25.9859908Z","@mt":"メール送信に失敗しました: 【エラー通知】BatchManagerNet - bat5","@l":"Error","@x":"MimeKit.ParseException: Unexpected token at offset 18\r\n   at MimeKit.MailboxAddress.Parse(ParserOptions options, String text)\r\n   at MimeKit.MailboxAddress.Parse(String text)\r\n   at BatchManager.Core.Services.EmailService.SendEmailAsync(String subject, String body, Boolean isHtml) in C:\\projects\\BatchManager\\src\\BatchManager.Core\\Services\\EmailService.cs:line 47"}
{"@t":"2025-10-01T09:45:25.9975660Z","@mt":"エラーメール送信に失敗しました: Unexpected token at offset 18","@l":"Warning"}
{"@t":"2025-10-01T09:45:25.9980020Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:45:25.9986212Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:45:25.9991167Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:45:25.9995417Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:45:25.9999571Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:45:26.0003370Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:45:26.0031034Z","@mt":"  実行時間: 0.08秒"}
{"@t":"2025-10-01T09:45:26.0036364Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:45:26.0046928Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:45:27.7180327Z","@mt":"バッチ実行履歴を登録しました (ID: 19)"}
{"@t":"2025-10-01T09:45:28.0663350Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:45:28.0698008Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:45:28.0712403Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:45:28.0727985Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:46:55.4841207Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:46:55.4982888Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:46:55.4984267Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:46:55.4984729Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:46:55.4984936Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:46:55.4985068Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:46:55.4985195Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:46:55.4985815Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:46:55.4986111Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4986266Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.4986383Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.4986500Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:46:55.4986639Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:46:55.4986846Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4987040Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:46:55.4987161Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:46:55.4987380Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:46:55.4987741Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:46:55.4987947Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:46:55.4988073Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:46:55.4988269Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:46:55.4988466Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4988586Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:46:55.4988711Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:46:55.4988922Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:46:55.4989042Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:46:55.4989185Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:46:55.4989337Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:46:55.4989518Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:46:55.4989633Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:46:55.4989770Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:46:55.4989878Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:46:55.4989980Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:46:55.4990101Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:46:55.4990492Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.4993429Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:46:55.4993836Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:46:55.5052578Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:46:55.5068774Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:46:55.5365135Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:46:55.5372244Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:46:55.5404708Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:46:55.5406320Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:46:55.5406890Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:46:55.5407061Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:46:55.5407389Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:46:55.5423510Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:46:55.5433207Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:46:57.5471462Z","@mt":"メールを送信しました: 【エラー通知】BatchManagerNet - bat5"}
{"@t":"2025-10-01T09:46:57.5477207Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:46:57.5485634Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:46:57.5487737Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:46:57.5488825Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:46:57.5489606Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:46:57.5490550Z","@mt":"  失敗: 0"}
{"@t":"2025-10-01T09:46:57.5517730Z","@mt":"  実行時間: 2.04秒"}
{"@t":"2025-10-01T09:46:57.5518940Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:46:57.5536709Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:46:59.4583525Z","@mt":"バッチ実行履歴を登録しました (ID: 20)"}
{"@t":"2025-10-01T09:46:59.8514118Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:46:59.8553811Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:46:59.8567578Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:46:59.8571060Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
{"@t":"2025-10-01T09:57:14.3330193Z","@mt":"ログサービスを初期化しました: BatchManagerNet.log"}
{"@t":"2025-10-01T09:57:14.3492735Z","@mt":"===== BatchManagerNet 起動 ====="}
{"@t":"2025-10-01T09:57:14.3494684Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:57:14.3495125Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:57:14.3495353Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:57:14.3495491Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:57:14.3495641Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:57:14.3495758Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:57:14.3496444Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.3496612Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.3496725Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.3496842Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:57:14.3496979Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:57:14.3497118Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.3497221Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:57:14.3497325Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:57:14.3497491Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:57:14.3497778Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:57:14.3497907Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:57:14.3498097Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:57:14.3498305Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:57:14.3498479Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.3498599Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:57:14.3498728Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:57:14.3498954Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:57:14.3499078Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:57:14.3499203Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:57:14.3499358Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:57:14.3499469Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:57:14.3499578Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:57:14.3499724Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:57:14.3499838Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:57:14.3499946Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:57:14.3502094Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:57:14.3503080Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.3507258Z","@mt":"インスタンス名: BatchManagerNet"}
{"@t":"2025-10-01T09:57:14.3507709Z","@mt":"設定ファイル: BatchManagerNet.config"}
{"@t":"2025-10-01T09:57:14.3562698Z","@mt":"===== フェーズ1: 初期化 ====="}
{"@t":"2025-10-01T09:57:14.3581131Z","@mt":"ジョブ設定ファイルを読み込んでいます: BatchManagerNet.config"}
{"@t":"2025-10-01T09:57:14.3859933Z","@mt":"ジョブ設定を読み込みました: 4件"}
{"@t":"2025-10-01T09:57:14.3864493Z","@mt":"ジョブ数: 4"}
{"@t":"2025-10-01T09:57:14.3880658Z","@mt":"===== フェーズ2: ジョブ実行 ====="}
{"@t":"2025-10-01T09:57:14.3881706Z","@mt":"[1/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat1.bat"}
{"@t":"2025-10-01T09:57:14.3882053Z","@mt":"[2/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat2.bat"}
{"@t":"2025-10-01T09:57:14.3882184Z","@mt":"[3/4] ジョブはスキップされました（無効）: C:\\projects\\BatchManager\\testbatch\\bat3.bat"}
{"@t":"2025-10-01T09:57:14.3882357Z","@mt":"[4/4] ジョブ実行開始: C:\\projects\\BatchManager\\testbatch\\bat5.bat"}
{"@t":"2025-10-01T09:57:14.3916924Z","@mt":"実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:57:14.3934302Z","@mt":"ジョブ実行エラー: 実行ファイルが見つかりません: C:\\projects\\BatchManager\\testbatch\\bat5.bat","@l":"Error"}
{"@t":"2025-10-01T09:57:16.1286837Z","@mt":"メールを送信しました: 【エラー通知】BatchManagerNet - bat5"}
{"@t":"2025-10-01T09:57:16.1289790Z","@mt":"===== フェーズ3: 完了処理 ====="}
{"@t":"2025-10-01T09:57:16.1292776Z","@mt":"バッチ実行完了:"}
{"@t":"2025-10-01T09:57:16.1294179Z","@mt":"  総ジョブ数: 4"}
{"@t":"2025-10-01T09:57:16.1294746Z","@mt":"  成功: 0"}
{"@t":"2025-10-01T09:57:16.1295122Z","@mt":"  タイムアウト: 0"}
{"@t":"2025-10-01T09:57:16.1295352Z","@mt":"  失敗: 1"}
{"@t":"2025-10-01T09:57:16.1305993Z","@mt":"  実行時間: 1.77秒"}
{"@t":"2025-10-01T09:57:16.1306535Z","@mt":"===== フェーズ4: DB転送 ====="}
{"@t":"2025-10-01T09:57:16.1313003Z","@mt":"データベースへのアップロードを開始します..."}
{"@t":"2025-10-01T09:57:18.2539304Z","@mt":"バッチ実行履歴を登録しました (ID: 21)"}
{"@t":"2025-10-01T09:57:18.6137545Z","@mt":"ジョブ実行履歴を一括挿入しました: 1件"}
{"@t":"2025-10-01T09:57:18.6144598Z","@mt":"ジョブ実行履歴を登録しました (1件)"}
{"@t":"2025-10-01T09:57:18.6145390Z","@mt":"データベースへのアップロード完了"}
{"@t":"2025-10-01T09:57:18.6146332Z","@mt":"===== BatchManagerNet 終了 (ExitCode: 1) ====="}
