namespace BatchManager.Core.Models;

/// <summary>
/// ジョブ実行履歴を表すモデル（データベースのjob_executionsテーブルに対応）
/// </summary>
public class JobExecution
{
    /// <summary>
    /// ジョブ実行ID（自動採番）
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 親バッチ実行ID（batch_executionsテーブルへの外部キー）
    /// </summary>
    public long BatchExecutionId { get; set; }

    /// <summary>
    /// ジョブ実行順序（1から始まる連番）
    /// </summary>
    public int JobSequence { get; set; }

    /// <summary>
    /// ジョブ名（実行したコマンドのファイル名）
    /// </summary>
    public string JobName { get; set; } = string.Empty;

    /// <summary>
    /// 実行したコマンドライン（フルパス）
    /// </summary>
    public string CommandLine { get; set; } = string.Empty;

    /// <summary>
    /// コマンドライン引数
    /// </summary>
    public string? Parameters { get; set; }

    /// <summary>
    /// ジョブ実行開始日時（UTC）
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// ジョブ実行終了日時（UTC）
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// ジョブのステータス（"Success", "Timeout", "Error", "Skipped"）
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// プロセスの終了コード
    /// </summary>
    public int? ExitCode { get; set; }

    /// <summary>
    /// タイムアウト設定（秒）
    /// </summary>
    public int TimeoutLimitSeconds { get; set; }

    /// <summary>
    /// 実行時間（秒）
    /// </summary>
    public decimal? ExecutionTimeSeconds { get; set; }

    /// <summary>
    /// タイムアウト時の終了フラグ（1-4）
    /// </summary>
    public int EndFlag { get; set; }

    /// <summary>
    /// タイムアウトが発生したか
    /// </summary>
    public bool IsTimeout { get; set; }

    /// <summary>
    /// エラーメッセージ（あれば）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 標準出力（必要に応じて保存）
    /// </summary>
    public string? StandardOutput { get; set; }

    /// <summary>
    /// 標準エラー出力（必要に応じて保存）
    /// </summary>
    public string? StandardError { get; set; }

    /// <summary>
    /// レコード作成日時（UTC）
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 親バッチ実行（ナビゲーションプロパティ）
    /// </summary>
    public BatchExecution? BatchExecution { get; set; }

    /// <summary>
    /// ジョブ実行の経過時間（計算プロパティ）
    /// </summary>
    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
}



