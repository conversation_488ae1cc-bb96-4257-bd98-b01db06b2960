-- BatchManagerNet データベーススキーマ (NeonDB / PostgreSQL)
-- 作成日: 2025年10月1日
-- バージョン: 1.0

-- ==============================================
-- テーブル: batch_executions
-- バッチ実行履歴を記録
-- ==============================================
CREATE TABLE IF NOT EXISTS batch_executions (
    id BIGSERIAL PRIMARY KEY,
    instance_name VARCHAR(100) NOT NULL,
    customer_name VARCHAR(200),
    batch_description VARCHAR(500),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Success', 'Warning', 'Error')),
    total_jobs INT NOT NULL DEFAULT 0,
    successful_jobs INT NOT NULL DEFAULT 0,
    failed_jobs INT NOT NULL DEFAULT 0,
    timeout_jobs INT NOT NULL DEFAULT 0,
    total_duration_seconds NUMERIC(10, 2),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ==============================================
-- テーブル: job_executions
-- ジョブ実行履歴を記録（batch_executionsの子テーブル）
-- ==============================================
CREATE TABLE IF NOT EXISTS job_executions (
    id BIGSERIAL PRIMARY KEY,
    batch_execution_id BIGINT NOT NULL,
    job_sequence INT NOT NULL,
    job_name VARCHAR(200) NOT NULL,
    command_line TEXT NOT NULL,
    parameters TEXT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Success', 'Failed', 'Timeout', 'Skipped')),
    exit_code INT,
    timeout_limit_seconds INT,
    execution_time_seconds NUMERIC(10, 2),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外部キー制約
    CONSTRAINT fk_batch_execution
        FOREIGN KEY (batch_execution_id)
        REFERENCES batch_executions(id)
        ON DELETE CASCADE
);

-- ==============================================
-- インデックス
-- ==============================================

-- batch_executionsテーブル用インデックス
CREATE INDEX IF NOT EXISTS idx_batch_instance_time 
    ON batch_executions(instance_name, start_time DESC);

CREATE INDEX IF NOT EXISTS idx_batch_status 
    ON batch_executions(status);

CREATE INDEX IF NOT EXISTS idx_batch_start_time 
    ON batch_executions(start_time DESC);

-- job_executionsテーブル用インデックス
CREATE INDEX IF NOT EXISTS idx_job_batch_id 
    ON job_executions(batch_execution_id);

CREATE INDEX IF NOT EXISTS idx_job_name_time 
    ON job_executions(job_name, start_time DESC);

CREATE INDEX IF NOT EXISTS idx_job_status 
    ON job_executions(status);

CREATE INDEX IF NOT EXISTS idx_job_start_time 
    ON job_executions(start_time DESC);

-- ==============================================
-- ビュー: v_job_statistics
-- ジョブ別の統計情報を提供（GUI統計表示用）
-- ==============================================
CREATE OR REPLACE VIEW v_job_statistics AS
SELECT 
    job_name,
    COUNT(*) AS total_executions,
    SUM(CASE WHEN status = 'Success' THEN 1 ELSE 0 END) AS successful_executions,
    SUM(CASE WHEN status = 'Failed' THEN 1 ELSE 0 END) AS failed_executions,
    SUM(CASE WHEN status = 'Timeout' THEN 1 ELSE 0 END) AS timeout_executions,
    ROUND(AVG(execution_time_seconds), 2) AS avg_execution_time,
    ROUND(MIN(execution_time_seconds), 2) AS min_execution_time,
    ROUND(MAX(execution_time_seconds), 2) AS max_execution_time,
    ROUND(
        (SUM(CASE WHEN status = 'Success' THEN 1 ELSE 0 END)::NUMERIC / COUNT(*)::NUMERIC) * 100, 
        2
    ) AS success_rate,
    MAX(start_time) AS last_execution_time
FROM job_executions
WHERE start_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY job_name
ORDER BY total_executions DESC;

-- ==============================================
-- コメント
-- ==============================================

COMMENT ON TABLE batch_executions IS 'バッチ実行履歴テーブル';
COMMENT ON COLUMN batch_executions.id IS 'バッチ実行ID（自動採番）';
COMMENT ON COLUMN batch_executions.instance_name IS 'BatchManagerNetインスタンス名';
COMMENT ON COLUMN batch_executions.customer_name IS '顧客名';
COMMENT ON COLUMN batch_executions.batch_description IS 'バッチの説明';
COMMENT ON COLUMN batch_executions.start_time IS 'バッチ開始日時（UTC）';
COMMENT ON COLUMN batch_executions.end_time IS 'バッチ終了日時（UTC）';
COMMENT ON COLUMN batch_executions.status IS 'バッチ全体のステータス（Success/Warning/Error）';
COMMENT ON COLUMN batch_executions.total_jobs IS '実行されたジョブの総数';
COMMENT ON COLUMN batch_executions.successful_jobs IS '成功したジョブ数';
COMMENT ON COLUMN batch_executions.failed_jobs IS '失敗したジョブ数';
COMMENT ON COLUMN batch_executions.timeout_jobs IS 'タイムアウトしたジョブ数';
COMMENT ON COLUMN batch_executions.total_duration_seconds IS 'バッチ全体の実行時間（秒）';

COMMENT ON TABLE job_executions IS 'ジョブ実行履歴テーブル';
COMMENT ON COLUMN job_executions.id IS 'ジョブ実行ID（自動採番）';
COMMENT ON COLUMN job_executions.batch_execution_id IS '親バッチ実行ID';
COMMENT ON COLUMN job_executions.job_sequence IS 'ジョブ実行順序（1から始まる）';
COMMENT ON COLUMN job_executions.job_name IS 'ジョブ名（実行ファイル名）';
COMMENT ON COLUMN job_executions.command_line IS '実行したコマンドライン（フルパス）';
COMMENT ON COLUMN job_executions.parameters IS 'コマンドライン引数';
COMMENT ON COLUMN job_executions.status IS 'ジョブのステータス（Success/Failed/Timeout/Skipped）';
COMMENT ON COLUMN job_executions.exit_code IS 'ジョブの終了コード';
COMMENT ON COLUMN job_executions.timeout_limit_seconds IS 'タイムアウト制限時間（秒）';
COMMENT ON COLUMN job_executions.execution_time_seconds IS 'ジョブ実行時間（秒）';

COMMENT ON VIEW v_job_statistics IS 'ジョブ別統計情報ビュー（直近30日）';

