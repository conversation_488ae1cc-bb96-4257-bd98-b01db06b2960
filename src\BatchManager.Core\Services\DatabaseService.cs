using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;
using Dapper;
using Npgsql;

namespace BatchManager.Core.Services;

/// <summary>
/// PostgreSQL/NeonDBへのデータベース操作を行うサービス
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly string _connectionString;
    private readonly ILogService _logService;

    public DatabaseService(string connectionString, ILogService logService)
    {
        _connectionString = connectionString;
        _logService = logService;
    }

    /// <summary>
    /// データベース接続をテストする
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logService.Debug("データベース接続をテストしています...");
            
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            
            var result = await connection.ExecuteScalarAsync<int>("SELECT 1");
            
            _logService.Information("データベース接続テストに成功しました");
            return result == 1;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "データベース接続テストに失敗しました");
            return false;
        }
    }

    /// <summary>
    /// バッチ実行履歴を挿入する
    /// </summary>
    public async Task<long> InsertBatchExecutionAsync(BatchExecution batchExecution)
    {
        try
        {
            const string sql = @"
                INSERT INTO batch_executions 
                (instance_name, customer_name, batch_description, start_time, end_time, status, 
                 total_jobs, successful_jobs, failed_jobs, timeout_jobs, total_duration_seconds, error_message)
                VALUES 
                (@InstanceName, @CustomerName, @BatchDescription, @StartTime, @EndTime, @Status, 
                 @TotalJobs, @SuccessfulJobs, @FailedJobs, @TimeoutJobs, @TotalDurationSeconds, @ErrorMessage)
                RETURNING id";

            using var connection = new NpgsqlConnection(_connectionString);
            var id = await connection.ExecuteScalarAsync<long>(sql, batchExecution);
            
            _logService.Debug($"バッチ実行履歴を挿入しました: ID={id}");
            return id;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "バッチ実行履歴の挿入に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// バッチ実行履歴を更新する
    /// </summary>
    public async Task UpdateBatchExecutionAsync(BatchExecution batchExecution)
    {
        try
        {
            const string sql = @"
                UPDATE batch_executions 
                SET end_time = @EndTime,
                    status = @Status,
                    total_jobs = @TotalJobs,
                    successful_jobs = @SuccessfulJobs,
                    failed_jobs = @FailedJobs,
                    timeout_jobs = @TimeoutJobs,
                    total_duration_seconds = @TotalDurationSeconds,
                    error_message = @ErrorMessage
                WHERE id = @Id";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.ExecuteAsync(sql, batchExecution);
            
            _logService.Debug($"バッチ実行履歴を更新しました: ID={batchExecution.Id}");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "バッチ実行履歴の更新に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// ジョブ実行履歴を挿入する
    /// </summary>
    public async Task<long> InsertJobExecutionAsync(JobExecution jobExecution)
    {
        try
        {
            const string sql = @"
                INSERT INTO job_executions 
                (batch_execution_id, job_sequence, job_name, command_line, parameters, 
                 start_time, end_time, status, exit_code, timeout_limit_seconds, 
                 execution_time_seconds, error_message)
                VALUES 
                (@BatchExecutionId, @JobSequence, @JobName, @CommandLine, @Parameters, 
                 @StartTime, @EndTime, @Status, @ExitCode, @TimeoutLimitSeconds, 
                 @ExecutionTimeSeconds, @ErrorMessage)
                RETURNING id";

            using var connection = new NpgsqlConnection(_connectionString);
            var id = await connection.ExecuteScalarAsync<long>(sql, jobExecution);
            
            _logService.Debug($"ジョブ実行履歴を挿入しました: ID={id}");
            return id;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ジョブ実行履歴の挿入に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// バッチ実行履歴を取得する
    /// </summary>
    public async Task<BatchExecution?> GetBatchExecutionAsync(long id)
    {
        try
        {
            const string sql = "SELECT * FROM batch_executions WHERE id = @Id";

            using var connection = new NpgsqlConnection(_connectionString);
            var execution = await connection.QueryFirstOrDefaultAsync<BatchExecution>(sql, new { Id = id });
            
            _logService.Debug($"バッチ実行履歴を取得しました: ID={id}");
            return execution;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "バッチ実行履歴の取得に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// 指定期間のバッチ実行履歴を取得する
    /// </summary>
    public async Task<List<BatchExecution>> GetBatchExecutionsByPeriodAsync(
        DateTime startDate, 
        DateTime endDate, 
        string? instanceName = null)
    {
        try
        {
            var sql = @"
                SELECT * FROM batch_executions 
                WHERE start_time >= @StartDate AND start_time <= @EndDate";

            if (!string.IsNullOrEmpty(instanceName))
            {
                sql += " AND instance_name = @InstanceName";
            }

            sql += " ORDER BY start_time DESC";

            using var connection = new NpgsqlConnection(_connectionString);
            var executions = await connection.QueryAsync<BatchExecution>(
                sql, 
                new { StartDate = startDate, EndDate = endDate, InstanceName = instanceName });
            
            _logService.Debug($"期間指定バッチ実行履歴を取得しました: {executions.Count()}件");
            return executions.ToList();
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "期間指定バッチ実行履歴の取得に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// ジョブ統計情報を取得する
    /// </summary>
    public async Task<List<JobStatistics>> GetJobStatisticsAsync(string? instanceName = null, int days = 30)
    {
        try
        {
            var sql = @"
                SELECT 
                    je.job_name AS JobName,
                    COUNT(*) AS TotalExecutions,
                    SUM(CASE WHEN je.status = 'Success' THEN 1 ELSE 0 END) AS SuccessCount,
                    SUM(CASE WHEN je.status = 'Timeout' THEN 1 ELSE 0 END) AS TimeoutCount,
                    SUM(CASE WHEN je.status = 'Error' THEN 1 ELSE 0 END) AS ErrorCount,
                    CAST(SUM(CASE WHEN je.status = 'Success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DOUBLE PRECISION) AS SuccessRate,
                    AVG(je.execution_time_seconds) AS AverageExecutionSeconds,
                    MAX(je.execution_time_seconds) AS MaxExecutionSeconds,
                    MIN(je.execution_time_seconds) AS MinExecutionSeconds
                FROM job_executions je
                INNER JOIN batch_executions be ON je.batch_execution_id = be.id
                WHERE be.start_time >= @StartDate";

            if (!string.IsNullOrEmpty(instanceName))
            {
                sql += " AND be.instance_name = @InstanceName";
            }

            sql += @"
                GROUP BY je.job_name
                ORDER BY COUNT(*) DESC";

            var startDate = DateTime.UtcNow.AddDays(-days);
            using var connection = new NpgsqlConnection(_connectionString);
            var statistics = await connection.QueryAsync<JobStatistics>(
                sql, 
                new { StartDate = startDate, InstanceName = instanceName });
            
            _logService.Debug($"ジョブ統計情報を取得しました: {statistics.Count()}件");
            return statistics.ToList();
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ジョブ統計情報の取得に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// 指定されたインスタンスの最近のバッチ実行履歴を取得する
    /// </summary>
    public async Task<List<BatchExecution>> GetRecentBatchExecutionsAsync(string instanceName, int limit = 10)
    {
        try
        {
            const string sql = @"
                SELECT * FROM batch_executions 
                WHERE instance_name = @InstanceName 
                ORDER BY start_time DESC 
                LIMIT @Limit";

            using var connection = new NpgsqlConnection(_connectionString);
            var executions = await connection.QueryAsync<BatchExecution>(sql, new { InstanceName = instanceName, Limit = limit });
            
            _logService.Debug($"バッチ実行履歴を取得しました: {executions.Count()}件");
            return executions.ToList();
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "バッチ実行履歴の取得に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// 指定されたバッチ実行IDに紐づくジョブ実行履歴を取得する
    /// </summary>
    public async Task<List<JobExecution>> GetJobExecutionsByBatchIdAsync(long batchExecutionId)
    {
        try
        {
            const string sql = @"
                SELECT * FROM job_executions 
                WHERE batch_execution_id = @BatchExecutionId 
                ORDER BY job_sequence ASC";

            using var connection = new NpgsqlConnection(_connectionString);
            var jobs = await connection.QueryAsync<JobExecution>(sql, new { BatchExecutionId = batchExecutionId });
            
            _logService.Debug($"ジョブ実行履歴を取得しました: {jobs.Count()}件");
            return jobs.ToList();
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ジョブ実行履歴の取得に失敗しました");
            throw;
        }
    }

    /// <summary>
    /// 複数のジョブ実行履歴を一括で挿入する（バッチ完了後の一括転送用）
    /// </summary>
    public async Task InsertJobExecutionsBatchAsync(IEnumerable<JobExecution> jobExecutions)
    {
        var jobList = jobExecutions?.ToList();
        if (jobList == null || jobList.Count == 0)
        {
            _logService.Warning("挿入するジョブ実行履歴がありません");
            return;
        }

        try
        {
            const string sql = @"
                INSERT INTO job_executions 
                (batch_execution_id, job_sequence, job_name, command_line, parameters, 
                 start_time, end_time, status, exit_code, timeout_limit_seconds, 
                 execution_time_seconds, error_message)
                VALUES 
                (@BatchExecutionId, @JobSequence, @JobName, @CommandLine, @Parameters, 
                 @StartTime, @EndTime, @Status, @ExitCode, @TimeoutLimitSeconds, 
                 @ExecutionTimeSeconds, @ErrorMessage)";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            foreach (var job in jobList)
            {
                job.Status = NormalizeJobStatus(job.Status);
            }

            using var transaction = await connection.BeginTransactionAsync();
            try
            {
                await connection.ExecuteAsync(sql, jobList, transaction);
                await transaction.CommitAsync();
                
                _logService.Information($"ジョブ実行履歴を一括挿入しました: {jobList.Count}件");
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ジョブ実行履歴の一括挿入に失敗しました");
            throw;
        }
    }

    private string NormalizeJobStatus(string? status)
    {
        if (string.IsNullOrWhiteSpace(status))
        {
            _logService.Warning("ジョブステータスが空のため 'Failed' として保存します");
            return "Failed";
        }

        var trimmed = status.Trim();
        var normalized = trimmed.ToLowerInvariant();

        return normalized switch
        {
            "success" or "succeeded" or "ok" or "true" or "1" => "Success",
            "timeout" or "timedout" => "Timeout",
            "skipped" or "skip" => "Skipped",
            "failed" or "fail" or "error" or "false" or "0" => "Failed",
            "running" => "Failed", // 実行中のジョブは通常失敗として扱う
            _ => HandleUnknownStatus(trimmed)
        };
    }

    private string HandleUnknownStatus(string status)
    {
        _logService.Warning($"未対応のジョブステータスを検出しました: {status}。'Failed'として保存します");
        return "Failed";
    }
}

