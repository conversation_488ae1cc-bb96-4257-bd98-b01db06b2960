# BatchManagerNet 最新化プロジェクト 要件定義書

## 1. プロジェクト概要

### 1.1 目的
既存のバッチマネージャーアプリケーション（BatchManagerNet.exe）を最新化し、以下の機能を追加する：
- 設定ファイルのGUIメンテナンス機能
- ログローテーション機能
- クラウドデータベースへの実行結果アップロード
- バッチ実行時間の統計情報管理

### 1.2 対象システム
- **アプリケーション構成：**
  - `BatchManagerNet.exe`：バッチ実行エンジン（コンソールアプリ）
  - `BatchConfigEditor.exe`：GUI設定エディター（WPFアプリ）
  - `BatchManager.Core.dll`：共通ライブラリ
- **プラットフォーム：** .NET 8（LTS）
- **対象OS：** Windows Server 2019以降 / Windows 10/11
- **配置環境：**
  - バッチ実行：本番サーバー（タスクスケジューラで定期実行）
  - エディター：管理者PC（設定メンテナンス・統計参照用）

### 1.3 現行システムの機能
- XML設定ファイルに基づくバッチジョブの順次実行
- タイムアウト監視機能
- タイムアウト時のメール通知
- ログファイルへの実行記録

---

## 2. 機能要件

### 2.1 アプリケーション構成

アプリケーションは以下の2つの実行ファイルで構成されます：

#### 2.1.1 BatchManagerNet.exe（バッチ実行エンジン）
- **目的：** バッチジョブの順次実行
- **起動方法：** コマンドラインまたはタスクスケジューラから実行
- **主な機能：**
  - 設定ファイル（BatchManagerNet.config）の読み込み
  - ジョブの順次実行とタイムアウト監視
  - ログ記録とログローテーション
  - データベースへの実行結果アップロード
  - エラー時のメール通知
- **実行例：**
  ```
  # 通常実行
  BatchManagerNet.exe
  
  # 特定の設定ファイルを指定
  BatchManagerNet.exe /config="D:\Custom\BatchManagerNet.config"
  
  # ログローテーションのみ実行
  BatchManagerNet.exe /rotate-logs
  ```

#### 2.1.2 BatchConfigEditor.exe（GUI設定エディター）
- **目的：** 設定ファイルのGUI編集と統計情報表示
- **起動方法：** デスクトップショートカットまたはエクスプローラーから実行
- **主な機能：**
  - ジョブリストの編集（追加・削除・並び替え）
  - 設定ファイルの読み込み・保存
  - 統計情報の表示とエクスポート
  - データベースからの実行履歴取得
  - バリデーションとプレビュー機能

#### 2.1.3 共通設定
`BatchManagerNet.exe.config`（および`BatchConfigEditor.exe.config`）に以下の設定を含める：

```xml
<appSettings>
  <!-- 基本設定 -->
  <add key="InstanceName" value="MasterInterval" />
  <add key="config" value="D:\BMNetMasterInterval\BatchManagerNet.config" />
  <add key="log" value="D:\BMNetMasterInterval\BatchManagerNet.log" />
</appSettings>
```

**注：** 両アプリケーションは共通の設定ファイルパスを参照します。

### 2.2 GUI管理機能（設定ファイルメンテナンス）

#### 2.2.1 画面構成
- **メイン画面：ジョブリスト管理**
  - ジョブ一覧（グリッド表示）
  - ジョブの追加・編集・削除・並び替え
  - 設定の保存・読み込み
  - バリデーション機能

#### 2.2.2 ジョブ編集項目
各ジョブについて以下の項目を編集可能にする：

| 項目名 | 型 | 必須 | 説明 |
|--------|-----|------|------|
| comLine | string | ○ | 実行するEXE/BATファイルのフルパス |
| param | string | - | パラメータ（type=1の場合はフォルダパス、type=2の場合は引数） |
| type | int | ○ | パラメータタイプ（1:フォルダ指定、2:引数直接指定） |
| limitTime | int | ○ | タイムアウト時間（分） |
| endFlg | int | ○ | タイムアウト時の動作（1〜4） |
| description | string | - | ジョブの説明（新規追加項目） |

#### 2.2.3 UI機能詳細
- **ジョブ一覧表示**
  - 実行順序、ジョブ名、タイムアウト設定を一覧表示
  - ダブルクリックで編集画面を開く
  
- **ジョブ追加・編集**
  - ファイル選択ダイアログでEXE/BATファイルを選択
  - パラメータ入力
  - タイムアウト時間設定（スピンボックス）
  - タイムアウト時の動作選択（ラジオボタンまたはコンボボックス）
  
- **ジョブ並び替え**
  - ドラッグ&ドロップまたは↑↓ボタンで実行順序を変更
  
- **バリデーション**
  - ファイルパスの存在チェック
  - 必須項目チェック
  - 数値範囲チェック

- **プレビュー機能**
  - 生成されるXMLのプレビュー表示

### 2.3 バッチ実行機能（既存機能の維持・強化）

#### 2.3.1 基本実行フロー
1. **初期化フェーズ**
   - 設定ファイル（BatchManagerNet.config）の読み込み
   - ログローテーションチェック
   - 前回失敗したDB転送データの検出・再送信（オプション）
   - batch_execution情報の生成（メモリ）

2. **ジョブ実行フェーズ**
   - ジョブリストの順次実行
   - 各ジョブの実行監視（タイムアウト検知）
   - 各ジョブの実行結果をメモリに蓄積
   - 実行結果のローカルログ記録
   - タイムアウト時のメール通知
   - タイムアウト時の動作制御（endFlgに基づく）

3. **完了フェーズ**
   - バッチ実行結果の集計（成功数、失敗数、タイムアウト数）
   - batch_execution情報の完成（メモリ）
   - 最終ログ出力

4. **DB転送フェーズ（非同期/別スレッド）**
   - バッチ実行完了後、別スレッドで実行
   - 蓄積した実行結果をクラウドDBへ一括アップロード
   - 転送成功/失敗のログ記録
   - 失敗時は一時ファイルに保存（次回リトライ用）
   
**重要：** ステップ4はバッチの終了コードに影響しません。DB転送が失敗してもバッチは正常終了として扱われます。

#### 2.3.2 タイムアウト時の動作（endFlg）
| endFlg | 動作 |
|--------|------|
| 1 | 対象アプリをKillし次のジョブへ |
| 2 | 対象アプリをKillせず次のジョブへ |
| 3 | 対象アプリをKillしBatchManagerNetを終了 |
| 4 | 対象アプリをKillせずBatchManagerNetを終了 |

### 2.4 ログローテーション機能（新規）

#### 2.4.1 ログローテーション設定
`BatchManagerNet.exe.config`に以下の設定を追加：

```xml
<appSettings>
  <!-- ログローテーション設定 -->
  <add key="LogRotationEnabled" value="true" />
  <add key="LogRetentionDays" value="30" />
  <add key="LogArchiveFolder" value="D:\BMNetMasterInterval\Archive" />
  <add key="LogRotationTime" value="00:00" /> <!-- 実行時刻（HH:mm形式） -->
</appSettings>
```

#### 2.4.2 ローテーション仕様
- **実行タイミング：**
  - バッチ実行開始時にチェック
  - または、指定時刻に日次実行（Windowsタスクスケジューラ連携または独自スケジューラ）

- **ローテーション処理：**
  1. 現在のログファイル（BatchManagerNet.log）の最終更新日時を確認
  2. 指定日数（LogRetentionDays）を超えた古いログをアーカイブ
  3. アーカイブファイル名形式：`BatchManagerNet_YYYYMMDD_HHmmss.log`
  4. アーカイブフォルダに移動
  5. 保持日数を超えたアーカイブファイルを削除

- **例：**
  - 保持日数：30日
  - 2025年10月1日に実行
  - 2025年9月1日以前のログファイルを削除

### 2.5 クラウドデータベース連携機能（新規）

#### 2.5.1 データベース選択肢
以下のいずれかを採用（推奨順）：

1. **Supabase（推奨）**
   - PostgreSQL互換
   - 無料枠：500MB、月間帯域幅1GB
   - REST API / PostgreSQL直接接続の両対応
   - 簡単なセットアップ

2. **Neon**
   - サーバーレスPostgreSQL
   - 無料枠：0.5GB、月間100時間のアクティブ時間
   - PostgreSQL完全互換

3. **ElephantSQL**
   - PostgreSQL as a Service
   - 無料枠：20MB
   - 小規模用途に適する

4. **Cloudflare D1**
   - SQLite互換
   - 無料枠：5GB、100万read/日
   - REST API経由でのアクセス

**推奨：Supabase** （機能性、無料枠の大きさ、使いやすさのバランスが良い）

#### 2.5.2 データベーススキーマ

**テーブル1：batch_executions（バッチ実行履歴）**

| カラム名 | 型 | NULL | 説明 |
|----------|-----|------|------|
| id | bigserial | NOT NULL | 主キー |
| instance_name | varchar(100) | NOT NULL | BatchManagerNetインスタンス識別名 |
| execution_start_time | timestamp | NOT NULL | バッチ実行開始日時 |
| execution_end_time | timestamp | NULL | バッチ実行終了日時 |
| status | varchar(20) | NOT NULL | 実行ステータス（Success/Failed/Timeout） |
| total_jobs | int | NOT NULL | 総ジョブ数 |
| success_jobs | int | NOT NULL | 成功ジョブ数 |
| failed_jobs | int | NOT NULL | 失敗ジョブ数 |
| timeout_jobs | int | NOT NULL | タイムアウトジョブ数 |
| error_message | text | NULL | エラーメッセージ |
| created_at | timestamp | NOT NULL | レコード作成日時（デフォルト：now()） |

**テーブル2：job_executions（ジョブ実行履歴）**

| カラム名 | 型 | NULL | 説明 |
|----------|-----|------|------|
| id | bigserial | NOT NULL | 主キー |
| batch_execution_id | bigint | NOT NULL | バッチ実行ID（外部キー） |
| instance_name | varchar(100) | NOT NULL | インスタンス識別名 |
| job_name | varchar(255) | NOT NULL | ジョブ名（comLineのファイル名） |
| job_path | text | NOT NULL | ジョブフルパス（comLine） |
| job_order | int | NOT NULL | 実行順序 |
| start_time | timestamp | NOT NULL | ジョブ開始日時 |
| end_time | timestamp | NULL | ジョブ終了日時 |
| duration_seconds | int | NULL | 実行時間（秒） |
| status | varchar(20) | NOT NULL | ステータス（Success/Failed/Timeout/Killed） |
| exit_code | int | NULL | 終了コード |
| timeout_minutes | int | NOT NULL | タイムアウト設定（分） |
| end_flag | int | NOT NULL | タイムアウト時の動作フラグ |
| error_message | text | NULL | エラーメッセージ |
| created_at | timestamp | NOT NULL | レコード作成日時 |

**インデックス：**
- `idx_batch_executions_instance` ON batch_executions(instance_name, execution_start_time)
- `idx_job_executions_instance` ON job_executions(instance_name, start_time)
- `idx_job_executions_batch` ON job_executions(batch_execution_id)
- `idx_job_executions_job_name` ON job_executions(job_name, start_time)

#### 2.5.3 データベース接続設定
`BatchManagerNet.exe.config`に以下の設定を追加：

```xml
<appSettings>
  <!-- データベース連携設定 -->
  <add key="DatabaseEnabled" value="true" />
  <add key="DatabaseType" value="PostgreSQL" /> <!-- PostgreSQL / Cloudflare -->
  
  <!-- PostgreSQL（Supabase等）の場合 -->
  <add key="DbConnectionString" value="Host=db.xxxxx.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password;SSL Mode=Require;" />
  
  <!-- または、Cloudflare D1の場合 -->
  <add key="CloudflareAccountId" value="your-account-id" />
  <add key="CloudflareApiToken" value="your-api-token" />
  <add key="CloudflareDatabaseId" value="your-database-id" />
  
  <!-- アップロード失敗時の動作 -->
  <add key="DbUploadFailAction" value="LogOnly" /> <!-- LogOnly / Retry / Abort -->
  <add key="DbUploadRetryCount" value="3" />
</appSettings>
```

#### 2.5.4 アップロード仕様（バッチ実行に影響しない設計）

**基本方針：**
- ✅ バッチ実行中はメモリ/ローカルに実行結果を蓄積
- ✅ 全ジョブ完了後、一括でクラウドDBへアップロード
- ✅ アップロード処理は別スレッド/別プロセスで実行
- ✅ アップロード失敗してもバッチ実行は成功扱い

**処理フロー：**

1. **バッチ実行中（ジョブ実行フェーズ）**
   ```
   ┌─────────────────────────────────────┐
   │ 1. バッチ開始                        │
   │    → batch_executions情報を生成     │
   │      （メモリ上に保持）              │
   ├─────────────────────────────────────┤
   │ 2. ジョブ1実行                       │
   │    → job_executions情報を生成       │
   │      （メモリ/リストに追加）         │
   ├─────────────────────────────────────┤
   │ 3. ジョブ2実行                       │
   │    → job_executions情報を生成       │
   │      （メモリ/リストに追加）         │
   ├─────────────────────────────────────┤
   │ ... 全ジョブ順次実行                 │
   ├─────────────────────────────────────┤
   │ 4. バッチ終了                        │
   │    → batch_executions情報を完成     │
   │    → ローカルログファイルに記録     │
   └─────────────────────────────────────┘
            ↓（バッチ実行完了）
   ┌─────────────────────────────────────┐
   │ 5. DB転送開始（非同期/別スレッド）  │
   │    → batch_executionsをINSERT       │
   │    → 全job_executionsを一括INSERT   │
   └─────────────────────────────────────┘
   ```

2. **DB転送処理（別スレッド/Fire-and-Forget）**
   - バッチ実行メインスレッドは終了済み
   - DB転送は別スレッドで実行
   - 転送成功/失敗をログに記録
   - 失敗時のリトライ処理（オプション）

3. **転送データの一時保存**
   - オプション1：メモリ上のリストに保持
   - オプション2：ローカル一時ファイル（JSON）に保存
     - `BatchManagerNet_upload_YYYYMMDD_HHmmss.json`
     - DB転送成功後に削除
     - 失敗時は次回起動時に再送信

**タイミング詳細：**

| タイミング | 処理内容 | 影響 |
|-----------|---------|------|
| ジョブ開始時 | job_execution情報を生成（メモリ） | バッチに影響なし |
| ジョブ終了時 | job_execution情報を完成（メモリ） | バッチに影響なし |
| バッチ終了時 | batch_execution情報を完成（メモリ） | バッチに影響なし |
| バッチ完了後 | 一時ファイルに保存（オプション） | バッチ終了済み |
| バッチ完了後 | DB転送開始（別スレッド） | バッチ終了済み |

**失敗時の処理：**

| 状況 | 動作 |
|------|------|
| DB接続失敗 | 一時ファイルに保存し、ログに記録。バッチは正常終了。 |
| INSERT失敗 | リトライ（設定可能回数）後、一時ファイル保存。 |
| 一時ファイル保存失敗 | エラーログのみ記録。DB転送データは失われる。 |
| 次回バッチ起動時 | 残存する一時ファイルを検出し、再送信を試みる。 |

**設定項目（BatchManagerNet.exe.config）：**

```xml
<appSettings>
  <!-- DB転送設定 -->
  <add key="DbUploadMode" value="AfterCompletion" /> <!-- AfterCompletion / Disabled -->
  <add key="DbUploadAsync" value="true" /> <!-- 非同期転送 -->
  <add key="DbUploadFailAction" value="SaveAndContinue" /> 
       <!-- SaveAndContinue:一時保存して続行 / LogOnly:ログのみ -->
  <add key="DbUploadRetryCount" value="3" />
  <add key="DbUploadRetryInterval" value="5" /> <!-- 秒 -->
  <add key="DbUploadTempFolder" value="D:\BMNetMasterInterval\UploadQueue" />
  <add key="DbUploadTimeout" value="30" /> <!-- 秒、この時間経過後は転送を諦める -->
</appSettings>
```

**メリット：**
- ✅ バッチ実行時間がDB転送に影響されない
- ✅ DB障害時でもバッチは正常に完了
- ✅ ネットワーク遅延の影響を受けない
- ✅ 転送失敗時も次回リトライ可能
- ✅ バッチの本来の責務（ジョブ実行）に集中

### 2.6 統計情報表示機能（新規）

#### 2.6.1 表示内容
GUI管理モードに「統計情報」タブを追加し、以下の情報を表示：

- **ダッシュボード：**
  - 直近30日間のバッチ実行成功率（円グラフ）
  - 各ジョブの平均実行時間（棒グラフ）
  - タイムアウト発生頻度（折れ線グラフ）
  - 直近の実行履歴（一覧表）

- **ジョブ別統計：**
  - ジョブ名
  - 総実行回数
  - 成功回数/失敗回数/タイムアウト回数
  - 平均実行時間/最短/最長
  - 最終実行日時
  - 成功率

- **フィルタ機能：**
  - 期間指定（直近7日/30日/90日/カスタム）
  - インスタンス名フィルタ
  - ジョブ名フィルタ

#### 2.6.2 データ取得
- データベースからSELECTクエリで集計
- キャッシュ機能（1時間程度）
- エクスポート機能（CSV/Excel）

---

## 3. 非機能要件

### 3.1 性能要件
- **GUI画面の起動時間：** 3秒以内
- **設定ファイルの読み込み・保存：** 1秒以内
- **バッチ実行時間：** DB転送の影響を受けない（転送は完了後に非同期実行）
- **データベース一括アップロード：** 設定可能なタイムアウト内に完了（デフォルト：30秒）
  - タイムアウト時は転送を中断し、一時ファイルに保存
- **ログローテーション：** 10秒以内
- **メモリ使用量：** 実行結果の蓄積による影響を最小化（1バッチあたり数MB程度）

### 3.2 可用性要件
- **バッチ実行の独立性：** データベース接続失敗時もバッチ実行は正常に完了
- **DB転送の非同期性：** DB転送はバッチ完了後の別スレッドで実行され、バッチ実行時間に影響しない
- **リトライ機能：** DB転送失敗時は一時ファイルに保存し、次回バッチ起動時に再送信
- **設定ファイルの検証：** 設定ファイル不正時はエラーログを出力し終了
- **タイムアウト処理：** タイムアウト時は設定（endFlg）に従って適切に処理
- **ローカルログ優先：** クラウドDBが利用不可でも、ローカルログには必ず記録

### 3.3 セキュリティ要件
- データベース接続文字列の暗号化（推奨）
- メール送信パスワードの暗号化（推奨）
- GUI管理モードへのアクセス制御（オプション）

### 3.4 保守性要件
- ログファイルは構造化ログ形式（JSON推奨）
- エラーハンドリングの徹底
- 設定ファイルのバリデーション
- データベーススキーマのマイグレーション管理

### 3.5 互換性要件
- 既存の設定ファイル形式との互換性維持
- 既存のメール通知機能の維持
- 既存のログ形式との互換性（オプション：新形式への移行）

---

## 4. システム構成

### 4.1 アーキテクチャ（採用構成）

本プロジェクトでは、**分離型アーキテクチャ**を採用します：

```
【配置構成】
BatchManager/
├── BatchManagerNet.exe           （バッチ実行エンジン）
├── BatchManagerNet.exe.config    （バッチ実行設定）
├── BatchConfigEditor.exe         （GUI設定エディター）
├── BatchConfigEditor.exe.config  （エディター設定）
├── BatchManager.Core.dll         （共通ライブラリ）
├── BatchManagerNet.config        （ジョブ設定：共有）
└── BatchManagerNet.log           （ログファイル：共有）
```

**採用理由：**
- ✅ 各コンポーネントが独立し、保守性が高い
- ✅ バッチ実行環境ではGUI不要で軽量
- ✅ エディターとバッチを別々にバージョン管理可能
- ✅ セキュリティ：本番環境にエディターを配置不要
- ✅ 責任分離原則（SRP）に準拠

**構成詳細：**

1. **BatchManagerNet.exe（バッチ実行専用）**
   - 依存関係：BatchManager.Core.dll
   - 機能：ジョブ実行、ログ、DB連携、メール送信
   - 配置場所：本番サーバー

2. **BatchConfigEditor.exe（GUI管理専用）**
   - 依存関係：BatchManager.Core.dll
   - 機能：設定編集、統計表示、DB参照
   - 配置場所：管理者PC / 開発環境

3. **BatchManager.Core.dll（共通ライブラリ）**
   - 含まれる機能：
     - データモデル（ComList、BatchExecution、JobExecution）
     - 設定ファイル読み書き
     - データベースアクセス
     - ログ管理
     - メール送信（バッチのみで使用）

### 4.2 技術スタック

#### 推奨構成（.NET 8）
- **フレームワーク：** .NET 8（LTS）
- **GUI：** WPF または Windows Forms（WPF推奨）
- **データベースアクセス：** 
  - Npgsql（PostgreSQL）
  - Dapper（軽量ORM）
  - または Entity Framework Core
- **ログ：** Serilog（構造化ログ）
- **メール送信：** MailKit（既存のSystem.Net.Mailの代替）
- **設定管理：** Microsoft.Extensions.Configuration
- **依存性注入：** Microsoft.Extensions.DependencyInjection

#### NuGetパッケージ
```xml
<PackageReference Include="Npgsql" Version="8.0.x" />
<PackageReference Include="Dapper" Version="2.1.x" />
<PackageReference Include="Serilog" Version="3.1.x" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.x" />
<PackageReference Include="Serilog.Sinks.Console" Version="5.0.x" />
<PackageReference Include="MailKit" Version="4.3.x" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.x" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.x" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.x" />
```

### 4.3 プロジェクト構成例

```
BatchManager/
├── src/
│   ├── BatchManager.Core/                    # 共通ライブラリ（クラスライブラリプロジェクト）
│   │   ├── Models/                           # データモデル
│   │   │   ├── ComList.cs                    # ジョブ設定モデル
│   │   │   ├── BatchExecution.cs             # バッチ実行履歴モデル
│   │   │   ├── JobExecution.cs               # ジョブ実行履歴モデル
│   │   │   └── AppSettings.cs                # 設定モデル
│   │   ├── Services/                         # サービス層
│   │   │   ├── ConfigService.cs              # 設定ファイル読み書き
│   │   │   ├── LogService.cs                 # ログ管理（Serilog）
│   │   │   ├── DatabaseService.cs            # DB接続・CRUD操作
│   │   │   ├── DatabaseUploadService.cs      # DB一括アップロード（非同期）
│   │   │   ├── UploadQueueService.cs         # 転送キュー管理（一時ファイル）
│   │   │   ├── EmailService.cs               # メール送信（MailKit）
│   │   │   └── LogRotationService.cs         # ログローテーション
│   │   ├── Interfaces/                       # インターフェース
│   │   │   ├── IConfigService.cs
│   │   │   ├── ILogService.cs
│   │   │   ├── IDatabaseService.cs
│   │   │   └── IEmailService.cs
│   │   └── Utilities/                        # ユーティリティ
│   │       ├── ProcessHelper.cs              # プロセス管理
│   │       └── ValidationHelper.cs           # バリデーション
│   │
│   ├── BatchManager.Executor/                # バッチ実行アプリ（コンソールアプリ）
│   │   ├── Program.cs                        # エントリーポイント
│   │   ├── BatchRunner.cs                    # バッチ実行制御
│   │   ├── JobExecutor.cs                    # ジョブ実行・監視
│   │   ├── TimeoutMonitor.cs                 # タイムアウト検知
│   │   ├── ExecutionResultCollector.cs       # 実行結果の蓄積（メモリ）
│   │   ├── UploadCoordinator.cs              # DB転送調整（非同期起動）
│   │   └── App.config                        # アプリ設定
│   │       → ビルド後: BatchManagerNet.exe.config
│   │
│   └── BatchManager.ConfigEditor/            # GUI設定エディター（WPFアプリ）
│       ├── App.xaml                          # アプリケーション定義
│       ├── App.xaml.cs
│       ├── Views/                            # 画面（XAML）
│       │   ├── MainWindow.xaml               # メイン画面
│       │   ├── JobEditDialog.xaml            # ジョブ編集ダイアログ
│       │   ├── StatisticsView.xaml           # 統計情報タブ
│       │   └── SettingsView.xaml             # 設定タブ
│       ├── ViewModels/                       # ビューモデル（MVVM）
│       │   ├── MainViewModel.cs
│       │   ├── JobEditViewModel.cs
│       │   ├── StatisticsViewModel.cs
│       │   └── SettingsViewModel.cs
│       ├── Converters/                       # 値コンバーター
│       ├── Commands/                         # コマンド
│       └── App.config                        # アプリ設定
│           → ビルド後: BatchConfigEditor.exe.config
│
├── database/
│   ├── schema.sql                            # データベーススキーマ（PostgreSQL）
│   └── sample_data.sql                       # テストデータ
│
├── config/                                    # 設定ファイルサンプル
│   ├── BatchManagerNet.exe.config.sample     # バッチ実行設定サンプル
│   ├── BatchConfigEditor.exe.config.sample   # エディター設定サンプル
│   └── BatchManagerNet.config.sample         # ジョブ設定サンプル
│
├── deploy/                                    # デプロイ用
│   ├── batch/                                # バッチ実行用（本番配置）
│   │   ├── BatchManagerNet.exe
│   │   ├── BatchManagerNet.exe.config
│   │   ├── BatchManager.Core.dll
│   │   └── (その他の依存DLL)
│   │
│   └── editor/                               # エディター用（管理者PC配置）
│       ├── BatchConfigEditor.exe
│       ├── BatchConfigEditor.exe.config
│       ├── BatchManager.Core.dll
│       └── (その他の依存DLL)
│
├── tests/                                     # テストプロジェクト
│   ├── BatchManager.Core.Tests/
│   ├── BatchManager.Executor.Tests/
│   └── BatchManager.ConfigEditor.Tests/
│
└── docs/
    ├── 要件定義書.md
    ├── 設計書.md
    ├── 操作マニュアル_バッチ実行.md
    ├── 操作マニュアル_エディター.md
    └── セットアップガイド.md
```

**プロジェクト参照関係：**
```
BatchManager.Executor → BatchManager.Core
BatchManager.ConfigEditor → BatchManager.Core
```

**ビルド出力：**
- `BatchManagerNet.exe` + `BatchManager.Core.dll` → バッチ実行環境へ配置
- `BatchConfigEditor.exe` + `BatchManager.Core.dll` → 管理者PCへ配置

---

## 5. 設定ファイル詳細

### 5.1 BatchManagerNet.exe.config（完全版）

```xml
<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <system.diagnostics>
    <sources>
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog"/>
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information" />
    </switches>
    <sharedListeners>
      <add name="FileLog"
           type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" 
           initializeData="FileLogWriter"/>
    </sharedListeners>
  </system.diagnostics>

  <appSettings>
    <!-- 基本設定 -->
    <add key="InstanceName" value="MasterInterval" />
    <add key="config" value="D:\BMNetMasterInterval\BatchManagerNet.config" />
    <add key="log" value="D:\BMNetMasterInterval\BatchManagerNet.log" />
    
    <!-- メール設定 -->
    <add key="FromMailAddress" value="<EMAIL>" />
    <add key="ToMailAddress" value="<EMAIL>" />
    <add key="SmtpClient" value="smtp.alpha-prm.jp" />
    <add key="User" value="noreply%rtsc.co.jp" />
    <add key="Pass" value="al_noreply001" />
    <add key="EnableSsl" value="1" />
    <add key="Port" value="587" />
    
    <!-- ログローテーション設定 -->
    <add key="LogRotationEnabled" value="true" />
    <add key="LogRetentionDays" value="30" />
    <add key="LogArchiveFolder" value="D:\BMNetMasterInterval\Archive" />
    <add key="LogRotationTime" value="00:00" />
    
    <!-- データベース連携設定 -->
    <add key="DatabaseEnabled" value="true" />
    <add key="DatabaseType" value="PostgreSQL" />
    <add key="DbConnectionString" value="Host=db.xxxxx.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password;SSL Mode=Require;" />
    
    <!-- DB転送設定（バッチ完了後に一括転送） -->
    <add key="DbUploadMode" value="AfterCompletion" />
    <add key="DbUploadAsync" value="true" />
    <add key="DbUploadFailAction" value="SaveAndContinue" />
    <add key="DbUploadRetryCount" value="3" />
    <add key="DbUploadRetryInterval" value="5" />
    <add key="DbUploadTempFolder" value="D:\BMNetMasterInterval\UploadQueue" />
    <add key="DbUploadTimeout" value="30" />
    
    <!-- ログ設定 -->
    <add key="LogFormat" value="JSON" /> <!-- JSON / Text -->
    <add key="LogLevel" value="Information" /> <!-- Verbose / Debug / Information / Warning / Error -->
  </appSettings>
</configuration>
```

### 5.2 BatchManagerNet.config（拡張版）

既存形式を維持しつつ、オプションで以下の項目を追加可能：

```xml
<?xml version="1.0"?>
<ArrayOfComList xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <comList>
    <comLine>D:\NewWebReportBatch\YS_I23_StoreMasterDiffUpdate\YS_I23_StoreMasterDiffUpdate.exe</comLine>
    <param></param>
    <type>2</type>
    <limitTime>120</limitTime>
    <endFlg>1</endFlg>
    
    <!-- 以下、拡張項目（オプション） -->
    <description>店舗マスター差分更新バッチ</description>
    <enabled>true</enabled>
    <retryCount>0</retryCount>
    <notifyOnSuccess>false</notifyOnSuccess>
  </comList>
  
  <!-- 他のジョブ... -->
</ArrayOfComList>
```

---

## 6. 画面設計

### 6.1 メイン画面（ジョブ管理）

```
┌─────────────────────────────────────────────────────────────┐
│ BatchManager Configuration Editor               [_][□][X] │
├─────────────────────────────────────────────────────────────┤
│ ファイル(F)  編集(E)  表示(V)  ツール(T)  ヘルプ(H)         │
├─────────────────────────────────────────────────────────────┤
│ [開く] [保存] [新規ジョブ] [削除] [↑] [↓] [テスト実行]    │
├─────────────────────────────────────────────────────────────┤
│ タブ: [ジョブ管理] [統計情報] [設定]                        │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│  ┌──────────────────────────────────────────────────────┐  │
│  │順│有効│ジョブ名                    │タイムアウト│動作│  │
│  ├──┼────┼──────────────────────┼──────────┼────┤  │
│  │1 │☑  │StoreMasterDiffUpdate   │120分      │Kill│  │
│  │2 │☑  │SupplierMasterDiffUpdate│120分      │Kill│  │
│  │3 │☑  │ItemMasterDiffUpdate    │120分      │Kill│  │
│  │4 │☑  │PluMaterDiffUpdate      │120分      │Kill│  │
│  │5 │☑  │StoreItemMasterDiffUpd..│120分      │Kill│  │
│  │  │    │                        │           │    │  │
│  └──────────────────────────────────────────────────────┘  │
│                                                               │
│  選択ジョブ詳細:                                             │
│  ┌──────────────────────────────────────────────────────┐  │
│  │ ファイルパス: D:\NewWebReportBatch\YS_I23_Store...    │  │
│  │ パラメータ  : (なし)                                   │  │
│  │ 説明        : 店舗マスター差分更新バッチ              │  │
│  │ 統計        : 平均実行時間 15.3分 / 成功率 98.5%     │  │
│  └──────────────────────────────────────────────────────┘  │
│                                                               │
├─────────────────────────────────────────────────────────────┤
│ インスタンス名: MasterInterval    最終保存: 2025/10/01 10:30│
└─────────────────────────────────────────────────────────────┘
```

### 6.2 ジョブ編集ダイアログ

```
┌───────────────────────────────────────┐
│ ジョブ編集                  [_][X]   │
├───────────────────────────────────────┤
│                                        │
│  ジョブ名:                             │
│  [StoreMasterDiffUpdate              ]│
│                                        │
│  実行ファイル: *                       │
│  [D:\NewWebReportBatch\YS_I23_Sto...] │
│  [参照...]                             │
│                                        │
│  パラメータタイプ: *                   │
│  ○ フォルダ指定  ● 引数直接指定       │
│                                        │
│  パラメータ:                           │
│  [                                   ]│
│                                        │
│  タイムアウト設定: *                   │
│  [120] 分                              │
│                                        │
│  タイムアウト時の動作: *               │
│  [Killして次へ進む ▼]                 │
│                                        │
│  説明:                                 │
│  ┌────────────────────────────────┐  │
│  │店舗マスター差分更新バッチ          │  │
│  │                                    │  │
│  └────────────────────────────────┘  │
│                                        │
│  □ 有効                                │
│                                        │
│        [OK]  [キャンセル]  [適用]      │
│                                        │
└───────────────────────────────────────┘
```

### 6.3 統計情報画面

```
┌─────────────────────────────────────────────────────────────┐
│ 統計情報                                                     │
├─────────────────────────────────────────────────────────────┤
│ 期間: [直近30日 ▼]  インスタンス: [すべて ▼]  [更新]      │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│  ┌─────────────────┐  ┌─────────────────────────────┐      │
│  │  実行成功率      │  │  ジョブ別平均実行時間       │      │
│  │                 │  │                             │      │
│  │   ┌─────┐     │  │  Job1 ■■■■■■ 15.3分 │      │
│  │   │     │     │  │  Job2 ■■■■■■ 12.8分 │      │
│  │   │ 98%│     │  │  Job3 ■■■■■■■ 18.2分│      │
│  │   │     │     │  │  Job4 ■■■■ 8.5分     │      │
│  │   └─────┘     │  │  Job5 ■■■■■ 11.1分   │      │
│  │                 │  │                             │      │
│  └─────────────────┘  └─────────────────────────────┘      │
│                                                               │
│  ジョブ別詳細統計:                                           │
│  ┌──────────────────────────────────────────────────────┐  │
│  │ジョブ名        │実行│成功│失敗│T/O│平均時間│成功率│    │
│  ├───────────────┼────┼────┼────┼───┼────────┼──────┤    │
│  │StoreMaster...  │ 30 │ 30 │ 0  │ 0 │ 15.3分│100%  │    │
│  │SupplierMast... │ 30 │ 29 │ 1  │ 0 │ 12.8分│96.7% │    │
│  │ItemMaster...   │ 30 │ 30 │ 0  │ 0 │ 18.2分│100%  │    │
│  └──────────────────────────────────────────────────────┘  │
│                                                               │
│  [CSVエクスポート]  [Excelエクスポート]                     │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 7. データベーススキーマ（SQL）

### 7.1 テーブル作成SQL（PostgreSQL）

```sql
-- バッチ実行履歴テーブル
CREATE TABLE batch_executions (
    id BIGSERIAL PRIMARY KEY,
    instance_name VARCHAR(100) NOT NULL,
    execution_start_time TIMESTAMP NOT NULL,
    execution_end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL,
    total_jobs INT NOT NULL,
    success_jobs INT NOT NULL,
    failed_jobs INT NOT NULL,
    timeout_jobs INT NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ジョブ実行履歴テーブル
CREATE TABLE job_executions (
    id BIGSERIAL PRIMARY KEY,
    batch_execution_id BIGINT NOT NULL,
    instance_name VARCHAR(100) NOT NULL,
    job_name VARCHAR(255) NOT NULL,
    job_path TEXT NOT NULL,
    job_order INT NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration_seconds INT,
    status VARCHAR(20) NOT NULL,
    exit_code INT,
    timeout_minutes INT NOT NULL,
    end_flag INT NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_execution_id) REFERENCES batch_executions(id) ON DELETE CASCADE
);

-- インデックス作成
CREATE INDEX idx_batch_executions_instance ON batch_executions(instance_name, execution_start_time DESC);
CREATE INDEX idx_job_executions_instance ON job_executions(instance_name, start_time DESC);
CREATE INDEX idx_job_executions_batch ON job_executions(batch_execution_id);
CREATE INDEX idx_job_executions_job_name ON job_executions(job_name, start_time DESC);

-- 統計情報用ビュー
CREATE VIEW v_job_statistics AS
SELECT 
    instance_name,
    job_name,
    COUNT(*) as total_executions,
    COUNT(CASE WHEN status = 'Success' THEN 1 END) as success_count,
    COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_count,
    COUNT(CASE WHEN status = 'Timeout' THEN 1 END) as timeout_count,
    AVG(duration_seconds) as avg_duration_seconds,
    MIN(duration_seconds) as min_duration_seconds,
    MAX(duration_seconds) as max_duration_seconds,
    MAX(start_time) as last_execution_time,
    ROUND(COUNT(CASE WHEN status = 'Success' THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC * 100, 2) as success_rate
FROM job_executions
GROUP BY instance_name, job_name;
```

---

## 8. 開発計画

### 8.1 開発フェーズ

#### フェーズ1：基盤構築（1-2週間）
- [ ] プロジェクト構成の作成
- [ ] 共通ライブラリの開発
  - [ ] 設定ファイル読み込み
  - [ ] ログ機能（Serilog導入）
  - [ ] データモデル定義
- [ ] データベーススキーマ作成
- [ ] 既存機能の移植（バッチ実行、メール送信）

#### フェーズ2：新機能開発（2-3週間）
- [ ] ログローテーション機能
- [ ] データベース連携機能
  - [ ] 接続管理
  - [ ] データアップロード
  - [ ] エラーハンドリング
- [ ] 起動モード切替機能

#### フェーズ3：GUI開発（2-3週間）
- [ ] メイン画面（ジョブ管理）
  - [ ] ジョブ一覧表示
  - [ ] ジョブ追加・編集・削除
  - [ ] ジョブ並び替え
  - [ ] バリデーション
- [ ] ジョブ編集ダイアログ
- [ ] 統計情報画面
  - [ ] データ取得・表示
  - [ ] グラフ描画
  - [ ] エクスポート機能

#### フェーズ4：テスト・デバッグ（1-2週間）
- [ ] 単体テスト
- [ ] 結合テスト
- [ ] 実環境テスト
- [ ] パフォーマンステスト

#### フェーズ5：ドキュメント作成・リリース（1週間）
- [ ] 操作マニュアル
- [ ] 設定ガイド
- [ ] リリースノート
- [ ] デプロイ

**総開発期間：7-11週間**

### 8.2 優先順位付け（MVP: Minimum Viable Product）

#### 必須機能（MVP）
1. バッチ実行機能（既存機能維持）
2. 起動モード切替
3. GUI設定編集
4. ログローテーション
5. データベース連携（基本）

#### 推奨機能
6. 統計情報表示
7. エクスポート機能

#### オプション機能
8. 高度なグラフ表示
9. Webベース管理UI
10. リアルタイム監視

---

## 9. リスクと対策

### 9.1 技術的リスク

| リスク | 影響 | 対策 |
|--------|------|------|
| データベース接続の不安定性 | 低 | バッチ完了後の転送のため影響なし。一時ファイル保存とリトライ機能で対応 |
| DB転送データの喪失 | 中 | 一時ファイルへの保存、次回起動時の再送信、ローカルログには必ず記録 |
| 一時ファイルの蓄積 | 中 | 定期的なクリーンアップ、古い一時ファイルの削除機能 |
| 既存バッチとの互換性 | 高 | 既存設定ファイル形式の完全維持、段階的移行 |
| タイムアウト検知の精度 | 中 | Process監視の強化、定期的なヘルスチェック |
| メモリリークのリスク | 中 | 実行結果の適切な破棄、メモリ使用量の監視 |

### 9.2 運用リスク

| リスク | 影響 | 対策 |
|--------|------|------|
| 設定ファイルの誤編集 | 高 | バックアップ機能、バリデーション強化、ロールバック機能 |
| ログの肥大化 | 中 | ローテーション機能、圧縮保存 |
| 一時転送ファイルの蓄積 | 中 | 自動クリーンアップ機能、転送成功後の即削除、保持期間設定（例：7日） |
| データベース容量超過 | 中 | 定期的なデータ削除、アラート機能 |
| DB転送の長期失敗 | 低 | 転送失敗の監視とアラート、手動での確認・再送信手段の提供 |
| セキュリティ情報の漏洩 | 高 | 設定ファイルの暗号化、アクセス制御、一時ファイルの適切な権限設定 |

---

## 10. 補足・提案

### 10.1 採用した起動方式の詳細

**分離型アーキテクチャ（2つの独立したEXE）**を採用しました。

#### BatchManagerNet.exe（バッチ実行）
```bash
# 通常実行（デフォルト設定を使用）
BatchManagerNet.exe

# カスタム設定ファイルを指定
BatchManagerNet.exe /config="D:\Custom\BatchManagerNet.config"

# ログローテーションのみ実行
BatchManagerNet.exe /rotate-logs

# タスクスケジューラから実行する例
schtasks /create /tn "BatchManager" /tr "D:\BatchManager\BatchManagerNet.exe" /sc daily /st 01:00
```

#### BatchConfigEditor.exe（GUI設定エディター）
```bash
# 通常起動（GUIが表示される）
BatchConfigEditor.exe

# 特定の設定ファイルを開く
BatchConfigEditor.exe /open="D:\Custom\BatchManagerNet.config"

# 統計画面を直接開く
BatchConfigEditor.exe /stats
```

**この方式のメリット：**
- ✅ 責任が明確に分離され、理解しやすい
- ✅ バッチ実行はCUI専用で軽量
- ✅ エディターはGUI専用で機能が豊富
- ✅ 個別にバージョンアップ可能
- ✅ セキュリティ：本番環境にエディターを配置しない運用が可能

### 10.2 DB転送方式の実装パターン

採用した「バッチ完了後の一括転送」方式の実装パターンを2つ提案します。

#### パターンA：別スレッド方式（推奨）
```csharp
// BatchRunner.cs
public int Run()
{
    var results = new ExecutionResultCollector();
    
    // 1. バッチ実行フェーズ
    foreach (var job in jobs)
    {
        var jobResult = jobExecutor.Execute(job);
        results.Add(jobResult);
    }
    
    // 2. バッチ完了
    var batchResult = results.Complete();
    logger.LogInformation("バッチ完了");
    
    // 3. DB転送（別スレッドで非同期実行）
    Task.Run(async () => {
        try
        {
            await uploadService.UploadAsync(batchResult);
        }
        catch (Exception ex)
        {
            await queueService.SaveToFileAsync(batchResult);
            logger.LogError(ex, "DB転送失敗。一時ファイルに保存しました。");
        }
    });
    
    return batchResult.ExitCode; // すぐに終了コードを返す
}
```

**メリット：**
- ✅ バッチプロセスが即座に終了
- ✅ 実装がシンプル

**デメリット：**
- ⚠️ プロセス終了時に転送スレッドも終了する可能性

#### パターンB：別プロセス方式
```csharp
// BatchRunner.cs
public int Run()
{
    var results = new ExecutionResultCollector();
    
    // 1. バッチ実行フェーズ
    foreach (var job in jobs)
    {
        var jobResult = jobExecutor.Execute(job);
        results.Add(jobResult);
    }
    
    // 2. バッチ完了・一時ファイル保存
    var batchResult = results.Complete();
    var tempFile = queueService.SaveToFile(batchResult);
    logger.LogInformation("バッチ完了");
    
    // 3. 別プロセスでDB転送
    Process.Start(new ProcessStartInfo
    {
        FileName = "BatchManagerNet.exe",
        Arguments = $"/upload \"{tempFile}\"",
        CreateNoWindow = true,
        UseShellExecute = false
    });
    
    return batchResult.ExitCode;
}
```

**メリット：**
- ✅ 確実に転送処理が完了
- ✅ バッチプロセスと完全に独立

**デメリット：**
- ⚠️ 一時ファイルが必須
- ⚠️ 実装が複雑

**推奨：パターンA（別スレッド方式）** を基本とし、必要に応じて一時ファイル保存機能を併用。

### 10.3 クラウドデータベースの推奨構成

**Supabase + PostgreSQL**を強く推奨します。

**理由：**
1. 無料枠が十分（500MB、1GB転送/月）
2. PostgreSQL完全互換（標準的なSQL）
3. REST APIとPostgreSQL直接接続の両対応
4. 管理UIが優秀（データ確認・クエリ実行が容易）
5. 自動バックアップ
6. Row Level Security（RLS）によるセキュリティ

**セットアップ手順：**
1. Supabaseアカウント作成（無料）
2. 新規プロジェクト作成
3. SQL Editorでスキーマ実行
4. 接続情報をexe.configに設定

### 10.4 ログ形式の推奨

構造化ログ（JSON形式）を推奨します。

**例：**
```json
{
  "timestamp": "2025-10-01T10:30:00.123Z",
  "level": "Information",
  "instance": "MasterInterval",
  "job_name": "StoreMasterDiffUpdate",
  "event": "JobStarted",
  "job_path": "D:\\NewWebReportBatch\\YS_I23_StoreMasterDiffUpdate\\YS_I23_StoreMasterDiffUpdate.exe",
  "timeout_minutes": 120
}
```

**メリット：**
- パース・検索が容易
- ログ分析ツールと連携しやすい
- データベースとの親和性が高い

### 10.5 将来の拡張性

将来的な機能追加の候補：

1. **Webベース管理UI（ASP.NET Core）**
   - どこからでもアクセス可能
   - リアルタイム監視
   - マルチユーザー対応

2. **Slack/Teams通知**
   - メールに加えてチャット通知
   - リッチな通知（グラフ、ステータス）

3. **条件分岐実行**
   - 前ジョブの結果に応じて次のジョブを変更
   - 並列実行サポート

4. **スケジューラ機能**
   - アプリ内でスケジュール管理
   - cron式対応

5. **ジョブ依存関係管理**
   - DAG（有向非巡回グラフ）によるジョブフロー定義

---

## 11. 承認・レビュー

### 11.1 確認事項

この要件定義について、以下の点をご確認ください：

- [ ] 既存機能の互換性維持は十分か
- [ ] 新機能の優先順位は適切か
- [ ] データベースの選択は適切か
- [ ] GUI設計は使いやすいか
- [ ] セキュリティ要件は十分か
- [ ] 開発期間は妥当か

### 11.2 次のステップ

1. **要件定義のレビュー・承認**
   - ✅ 起動方式：分離型アーキテクチャを採用（確定）
   - 残りの要件について確認・承認

2. **詳細設計書の作成**
   - クラス設計（BatchManager.Core）
   - バッチ実行フロー設計（BatchManagerNet.exe）
   - GUI画面設計（BatchConfigEditor.exe）
   - データベース設計の詳細化

3. **環境構築**
   - Supabaseアカウント作成・DB構築
   - 開発環境セットアップ（Visual Studio / VS Code）
   - .NET 8 SDKインストール

4. **プロトタイプ開発**
   - BatchManager.Coreの基本実装
   - 簡易版BatchManagerNet.exeの実装
   - 簡易版BatchConfigEditor.exeの実装

5. **本開発開始**
   - フェーズ1から順次実装

---

## 付録A：用語集

| 用語 | 説明 |
|------|------|
| comList | バッチジョブ設定のXML要素 |
| comLine | 実行するEXE/BATファイルのパス |
| limitTime | タイムアウト時間（分単位） |
| endFlg | タイムアウト時の動作フラグ（1-4） |
| インスタンス名 | BatchManagerNetの実行環境を識別する名称 |
| ログローテーション | 古いログファイルをアーカイブし、新しいログファイルを開始する処理 |

---

## 付録B：参考資料

- Supabase公式ドキュメント: https://supabase.com/docs
- Serilogドキュメント: https://serilog.net/
- Npgsqlドキュメント: https://www.npgsql.org/doc/
- .NET 8ドキュメント: https://learn.microsoft.com/ja-jp/dotnet/

---

**文書管理情報**
- 作成日：2025年10月1日
- 最終更新日：2025年10月1日
- バージョン：1.2
- 作成者：AI Assistant
- ステータス：レビュー待ち

**更新履歴**
| バージョン | 更新日 | 更新内容 |
|-----------|--------|---------|
| 1.0 | 2025-10-01 | 初版作成 |
| 1.1 | 2025-10-01 | 起動方式を分離型アーキテクチャ（BatchManagerNet.exe + BatchConfigEditor.exe）に確定。関連セクションを全面改定（2.1、4.1、4.3、5.1、10.1） |
| 1.2 | 2025-10-01 | DB転送方式を「バッチ完了後の一括転送」に確定。バッチ実行への影響を排除する設計に変更（2.3.1、2.5.4、3.1、3.2、4.3、5.1を更新） |

