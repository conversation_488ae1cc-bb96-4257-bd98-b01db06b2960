using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Xml.Serialization;

namespace BatchManager.Core.Models;

/// <summary>
/// ジョブ設定を表すモデル（BatchManagerNet.configから読み込まれる）
/// </summary>
[XmlRoot("comList")]
public class ComList : INotifyPropertyChanged
{
    private string _comLine = string.Empty;
    private string _param = string.Empty;
    private int _type;
    private int _limitTime;
    private int _endFlg;
    private string? _description;
    private bool _enabled = true;
    private int _retryCount = 0;
    private bool _notifyOnSuccess = false;

    /// <summary>
    /// 実行するコマンドライン（batファイルまたはexeファイルのパス）
    /// </summary>
    [XmlElement("comLine")]
    public string ComLine
    {
        get => _comLine;
        set => SetProperty(ref _comLine, value);
    }

    /// <summary>
    /// コマンドライン引数
    /// </summary>
    [XmlElement("param")]
    public string Param
    {
        get => _param;
        set => SetProperty(ref _param, value);
    }

    /// <summary>
    /// 実行タイプ（1: バッチファイル, 2: EXEファイル）
    /// </summary>
    [XmlElement("type")]
    public int Type
    {
        get => _type;
        set => SetProperty(ref _type, value);
    }

    /// <summary>
    /// タイムアウト時間（秒）
    /// </summary>
    [XmlElement("limitTime")]
    public int LimitTime
    {
        get => _limitTime;
        set => SetProperty(ref _limitTime, value);
    }

    /// <summary>
    /// タイムアウト時の終了フラグ（1: 続行してメール通知, 2: 次へ進まずメール通知, 3: 強制終了してメール通知, 4: 続行のみでメール通知なし）
    /// </summary>
    [XmlElement("endFlg")]
    public int EndFlg
    {
        get => _endFlg;
        set => SetProperty(ref _endFlg, value);
    }

    /// <summary>
    /// ジョブの説明（オプション）
    /// </summary>
    [XmlElement("description")]
    public string? Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    /// <summary>
    /// ジョブの有効/無効（オプション、デフォルト: true）
    /// </summary>
    [XmlElement("enabled")]
    public bool Enabled
    {
        get => _enabled;
        set => SetProperty(ref _enabled, value);
    }

    /// <summary>
    /// リトライ回数（オプション、デフォルト: 0）
    /// </summary>
    [XmlElement("retryCount")]
    public int RetryCount
    {
        get => _retryCount;
        set => SetProperty(ref _retryCount, value);
    }

    /// <summary>
    /// 成功時にもメール通知するか（オプション、デフォルト: false）
    /// </summary>
    [XmlElement("notifyOnSuccess")]
    public bool NotifyOnSuccess
    {
        get => _notifyOnSuccess;
        set => SetProperty(ref _notifyOnSuccess, value);
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

/// <summary>
/// ComListのルート配列（BatchManagerNet.configのルート要素）
/// </summary>
[XmlRoot("ArrayOfComList")]
public class ComListArray
{
    /// <summary>
    /// ジョブ設定のリスト
    /// </summary>
    [XmlElement("comList")]
    public ComList[] ComLists { get; set; } = Array.Empty<ComList>();
}


