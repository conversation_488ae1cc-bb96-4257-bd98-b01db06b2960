﻿using BatchManager.ConfigEditor.ViewModels;
using BatchManager.Core.Interfaces;
using BatchManager.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using System.Configuration;
using System.IO;
using System.Windows;

namespace BatchManager.ConfigEditor;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private ServiceProvider? _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        var exeDirectory = AppDomain.CurrentDomain.BaseDirectory;
        var defaultConfig = ConfigurationManager.AppSettings["DefaultConfigPath"] ?? string.Empty;
        var logFileSetting = ConfigurationManager.AppSettings["LogFilePath"] ?? "logs\\BatchConfigEditor.log";
        var logLevel = ConfigurationManager.AppSettings["LogLevel"] ?? "Information";
        var logFormat = ConfigurationManager.AppSettings["LogFormat"] ?? "Text";

        var resolvedLogPath = ResolvePath(exeDirectory, logFileSetting);
        var logDirectory = Path.GetDirectoryName(resolvedLogPath);
        if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }

        var services = new ServiceCollection();
        services.AddSingleton(new EditorConfigPaths
        {
            DefaultConfigPath = ResolvePath(exeDirectory, defaultConfig)
        });
        services.AddSingleton<ILogService>(_ => new LogService(resolvedLogPath, logLevel, logFormat));
        services.AddSingleton<IConfigService, ConfigService>();
        services.AddSingleton<MainViewModel>();
        services.AddSingleton<MainWindow>(sp =>
        {
            var window = new MainWindow
            {
                DataContext = sp.GetRequiredService<MainViewModel>()
            };
            return window;
        });

        _serviceProvider = services.BuildServiceProvider();
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    private static string ResolvePath(string exeDirectory, string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            return string.Empty;
        }

        if (Path.IsPathRooted(path))
        {
            return path;
        }

        return Path.Combine(exeDirectory, path);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }
}

public class EditorConfigPaths
{
    public string? DefaultConfigPath { get; set; }
}
