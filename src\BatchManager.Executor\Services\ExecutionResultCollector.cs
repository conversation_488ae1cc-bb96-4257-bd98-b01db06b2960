using BatchManager.Core.Models;
using System.Collections.Generic;

namespace BatchManager.Executor.Services;

/// <summary>
/// バッチ実行結果を収集・集計するクラス
/// </summary>
public class ExecutionResultCollector
{
    private BatchExecution? _batchExecution;
    private readonly List<JobExecution> _jobExecutions = new();
    private readonly object _lock = new();

    /// <summary>
    /// バッチ実行の初期化
    /// </summary>
    public void Initialize(string instanceName, string? customerName, string? batchDescription, DateTime startTime, int totalJobs)
    {
        lock (_lock)
        {
            _batchExecution = new BatchExecution
            {
                InstanceName = instanceName,
                CustomerName = customerName,
                BatchDescription = batchDescription,
                StartTime = startTime,
                TotalJobs = totalJobs,
                SuccessfulJobs = 0,
                FailedJobs = 0,
                TimeoutJobs = 0,
                Status = "Running"
            };

            _jobExecutions.Clear();
        }
    }

    /// <summary>
    /// ジョブ実行結果を追加
    /// </summary>
    public void AddJobResult(JobExecution jobResult)
    {
        lock (_lock)
        {
            jobResult.Status = NormalizeStatus(jobResult.Status);
            _jobExecutions.Add(jobResult);

            if (_batchExecution != null)
            {
                switch (jobResult.Status)
                {
                    case "Success":
                        _batchExecution.SuccessfulJobs++;
                        break;
                    case "Timeout":
                        _batchExecution.TimeoutJobs++;
                        break;
                    case "Failed":
                        _batchExecution.FailedJobs++;
                        break;
                }
            }
        }
    }

    /// <summary>
    /// バッチ実行を完了し、最終結果を返す
    /// </summary>
    public BatchExecution Finalize(DateTime endTime, bool hasError)
    {
        lock (_lock)
        {
            if (_batchExecution == null)
            {
                throw new InvalidOperationException("Initialize が呼ばれていません");
            }

            _batchExecution.EndTime = endTime;
            _batchExecution.TotalDurationSeconds = (decimal)(endTime - _batchExecution.StartTime).TotalSeconds;

            // ステータスの決定
            if (_batchExecution.FailedJobs > 0 || hasError)
            {
                _batchExecution.Status = "Error";
            }
            else if (_batchExecution.TimeoutJobs > 0)
            {
                _batchExecution.Status = "Warning";
            }
            else
            {
                _batchExecution.Status = "Success";
            }

            return _batchExecution;
        }
    }

    /// <summary>
    /// 収集したジョブ実行結果を取得
    /// </summary>
    public List<JobExecution> GetJobResults()
    {
        lock (_lock)
        {
            return new List<JobExecution>(_jobExecutions);
        }
    }

    /// <summary>
    /// 現在のバッチ実行情報を取得
    /// </summary>
    public BatchExecution? GetBatchExecution()
    {
        lock (_lock)
        {
            return _batchExecution;
        }
    }

    private static string NormalizeStatus(string? status)
    {
        if (string.IsNullOrWhiteSpace(status))
        {
            return "Unknown";
        }

        var trimmed = status.Trim();
        var normalized = trimmed.ToLowerInvariant();

        return normalized switch
        {
            "success" or "succeeded" or "ok" or "true" or "1" => "Success",
            "failed" or "fail" or "error" or "false" or "0" => "Failed",
            "timeout" or "timedout" => "Timeout",
            "skipped" => "Skipped",
            "running" => "Failed", // 実行中のジョブは通常失敗として扱う
            _ => "Failed" // 不明なステータスは失敗として扱う
        };
    }
}
