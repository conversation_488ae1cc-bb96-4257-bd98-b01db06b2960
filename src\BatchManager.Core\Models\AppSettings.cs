namespace BatchManager.Core.Models;

/// <summary>
/// アプリケーション設定を表すモデル（BatchManagerNet.exe.configから読み込まれる）
/// </summary>
public class AppSettings
{
    // 基本設定
    public string InstanceName { get; set; } = "DefaultInstance";
    public string CustomerName { get; set; } = string.Empty;
    public string BatchDescription { get; set; } = string.Empty;
    public string ConfigFilePath { get; set; } = string.Empty;
    public string LogFilePath { get; set; } = string.Empty;

    // メール設定
    public bool EmailEnabled { get; set; } = true;
    public string EmailFrom { get; set; } = string.Empty;
    public string EmailTo { get; set; } = string.Empty;
    public string SmtpHost { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public bool SmtpEnableSsl { get; set; } = true;
    public string SmtpUsername { get; set; } = string.Empty;
    public string SmtpPassword { get; set; } = string.Empty;
    public string EmailSubjectPrefix { get; set; } = "[BatchManagerNet]";
    
    // 互換性のため旧プロパティ名も保持（非推奨）
    [Obsolete("Use EmailFrom instead")]
    public string FromMailAddress 
    { 
        get => EmailFrom; 
        set => EmailFrom = value; 
    }
    
    [Obsolete("Use EmailTo instead")]
    public string ToMailAddress 
    { 
        get => EmailTo; 
        set => EmailTo = value; 
    }
    
    [Obsolete("Use SmtpHost instead")]
    public string SmtpClient 
    { 
        get => SmtpHost; 
        set => SmtpHost = value; 
    }
    
    [Obsolete("Use SmtpUsername instead")]
    public string User 
    { 
        get => SmtpUsername; 
        set => SmtpUsername = value; 
    }
    
    [Obsolete("Use SmtpPassword instead")]
    public string Pass 
    { 
        get => SmtpPassword; 
        set => SmtpPassword = value; 
    }
    
    [Obsolete("Use SmtpEnableSsl instead")]
    public bool EnableSsl 
    { 
        get => SmtpEnableSsl; 
        set => SmtpEnableSsl = value; 
    }
    
    [Obsolete("Use SmtpPort instead")]
    public int Port 
    { 
        get => SmtpPort; 
        set => SmtpPort = value; 
    }

    // ログローテーション設定
    public bool LogRotationEnabled { get; set; } = false;
    public int LogRetentionDays { get; set; } = 30;
    public string LogArchiveFolder { get; set; } = string.Empty;
    public TimeSpan LogRotationTime { get; set; } = TimeSpan.Zero;

    // データベース連携設定
    public bool DatabaseEnabled { get; set; } = false;
    public string DatabaseType { get; set; } = "PostgreSQL";
    public string DbConnectionString { get; set; } = string.Empty;

    // DB転送設定
    public string DbUploadMode { get; set; } = "AfterCompletion";
    public bool DbUploadAsync { get; set; } = true;
    public string DbUploadFailAction { get; set; } = "SaveAndContinue";
    public int DbUploadRetryCount { get; set; } = 3;
    public int DbUploadRetryInterval { get; set; } = 5;
    public string DbUploadTempFolder { get; set; } = string.Empty;
    public int DbUploadTimeout { get; set; } = 30;

    // ログ設定
    public string LogFormat { get; set; } = "JSON";
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 設定の妥当性を検証する
    /// </summary>
    /// <returns>検証結果のリスト（エラーがなければ空のリスト）</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(InstanceName))
            errors.Add("InstanceName is required.");

        if (string.IsNullOrWhiteSpace(ConfigFilePath))
            errors.Add("ConfigFilePath is required.");

        if (string.IsNullOrWhiteSpace(LogFilePath))
            errors.Add("LogFilePath is required.");

        if (LogRotationEnabled)
        {
            if (LogRetentionDays <= 0)
                errors.Add("LogRetentionDays must be greater than 0.");

            if (string.IsNullOrWhiteSpace(LogArchiveFolder))
                errors.Add("LogArchiveFolder is required when LogRotationEnabled is true.");
        }

        if (DatabaseEnabled)
        {
            if (string.IsNullOrWhiteSpace(DbConnectionString))
                errors.Add("DbConnectionString is required when DatabaseEnabled is true.");

            if (string.IsNullOrWhiteSpace(DbUploadTempFolder))
                errors.Add("DbUploadTempFolder is required when DatabaseEnabled is true.");
        }

        if (Port <= 0 || Port > 65535)
            errors.Add("Port must be between 1 and 65535.");

        if (DbUploadRetryCount < 0)
            errors.Add("DbUploadRetryCount must be 0 or greater.");

        if (DbUploadRetryInterval < 0)
            errors.Add("DbUploadRetryInterval must be 0 or greater.");

        if (DbUploadTimeout <= 0)
            errors.Add("DbUploadTimeout must be greater than 0.");

        return errors;
    }
}



