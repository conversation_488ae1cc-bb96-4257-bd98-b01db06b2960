using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;

namespace BatchManager.Core.Services;

/// <summary>
/// ログローテーションサービス
/// 古いログファイルをアーカイブし、指定日数分のログのみを保持する
/// </summary>
public class LogRotationService
{
    private readonly AppSettings _settings;
    private readonly ILogService _logService;

    public LogRotationService(AppSettings settings, ILogService logService)
    {
        _settings = settings;
        _logService = logService;
    }

    /// <summary>
    /// ログローテーションが必要かどうかをチェック
    /// </summary>
    public bool ShouldRotate()
    {
        if (!_settings.LogRotationEnabled)
        {
            return false;
        }

        try
        {
            // 設定されたローテーション時刻を取得
            var rotationTime = _settings.LogRotationTime;
            var now = DateTime.Now.TimeOfDay;

            // 現在時刻がローテーション時刻に近いかチェック（±5分以内）
            var timeDiff = Math.Abs((now - rotationTime).TotalMinutes);
            
            return timeDiff <= 5;
        }
        catch (Exception ex)
        {
            _logService.Warning($"ログローテーション時刻の解析に失敗しました: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// ログローテーションを実行する
    /// </summary>
    public async Task RotateLogsAsync()
    {
        if (!_settings.LogRotationEnabled)
        {
            _logService.Debug("ログローテーションは無効です");
            return;
        }

        try
        {
            _logService.Information("ログローテーションを開始します");

            // アーカイブフォルダの作成
            var archiveFolder = _settings.LogArchiveFolder;
            if (!Path.IsPathRooted(archiveFolder))
            {
                // 相対パスの場合、ログファイルと同じディレクトリに作成
                var logDir = Path.GetDirectoryName(_settings.LogFilePath) ?? ".";
                archiveFolder = Path.Combine(logDir, archiveFolder);
            }

            if (!Directory.Exists(archiveFolder))
            {
                Directory.CreateDirectory(archiveFolder);
                _logService.Debug($"アーカイブフォルダを作成しました: {archiveFolder}");
            }

            // ログファイルのディレクトリを取得
            var logDir2 = Path.GetDirectoryName(_settings.LogFilePath);
            if (string.IsNullOrEmpty(logDir2))
            {
                _logService.Warning("ログファイルのディレクトリが取得できません");
                return;
            }

            var logFileName = Path.GetFileNameWithoutExtension(_settings.LogFilePath);
            var logFileExt = Path.GetExtension(_settings.LogFilePath);

            // 古いログファイルを検索（例: BatchManagerNet.20250930.log）
            var logFiles = Directory.GetFiles(logDir2, $"{logFileName}*{logFileExt}")
                .Where(f => !f.Equals(_settings.LogFilePath, StringComparison.OrdinalIgnoreCase))
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.LastWriteTime)
                .ToList();

            _logService.Debug($"検出されたログファイル: {logFiles.Count}件");

            int archivedCount = 0;
            int deletedCount = 0;

            foreach (var logFile in logFiles)
            {
                var fileAge = DateTime.Now - logFile.LastWriteTime;

                if (fileAge.TotalDays > _settings.LogRetentionDays)
                {
                    // 保持期間を超えたログファイルはアーカイブ
                    var archiveFileName = $"{logFile.Name}_{logFile.LastWriteTime:yyyyMMdd}.archive";
                    var archivePath = Path.Combine(archiveFolder, archiveFileName);

                    try
                    {
                        // ファイルをアーカイブフォルダに移動
                        File.Move(logFile.FullName, archivePath, true);
                        archivedCount++;
                        _logService.Debug($"ログファイルをアーカイブしました: {logFile.Name} -> {archiveFileName}");
                    }
                    catch (Exception ex)
                    {
                        _logService.Warning($"ログファイルのアーカイブに失敗しました: {logFile.Name} - {ex.Message}");
                    }
                }
            }

            // アーカイブフォルダ内の古いファイルを削除（保持期間の2倍を超えたもの）
            var archiveFiles = Directory.GetFiles(archiveFolder, "*.archive")
                .Select(f => new FileInfo(f))
                .Where(f => (DateTime.Now - f.LastWriteTime).TotalDays > _settings.LogRetentionDays * 2)
                .ToList();

            foreach (var archiveFile in archiveFiles)
            {
                try
                {
                    File.Delete(archiveFile.FullName);
                    deletedCount++;
                    _logService.Debug($"古いアーカイブファイルを削除しました: {archiveFile.Name}");
                }
                catch (Exception ex)
                {
                    _logService.Warning($"アーカイブファイルの削除に失敗しました: {archiveFile.Name} - {ex.Message}");
                }
            }

            _logService.Information($"ログローテーションが完了しました（アーカイブ: {archivedCount}件, 削除: {deletedCount}件）");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ログローテーション中にエラーが発生しました");
        }
    }

    /// <summary>
    /// ディスク容量をチェックして、必要に応じて古いログを削除する
    /// </summary>
    public async Task CheckDiskSpaceAsync(long minimumFreeSpaceGB = 1)
    {
        try
        {
            var logDir = Path.GetDirectoryName(_settings.LogFilePath);
            if (string.IsNullOrEmpty(logDir))
            {
                return;
            }

            var drive = new DriveInfo(Path.GetPathRoot(logDir) ?? "C:\\");
            var freeSpaceGB = drive.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0);

            _logService.Debug($"ディスク空き容量: {freeSpaceGB:F2} GB");

            if (freeSpaceGB < minimumFreeSpaceGB)
            {
                _logService.Warning($"ディスク空き容量が少なくなっています: {freeSpaceGB:F2} GB");
                
                // 緊急ログクリーンアップ
                await EmergencyLogCleanupAsync();
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "ディスク容量チェック中にエラーが発生しました");
        }
    }

    /// <summary>
    /// 緊急時のログクリーンアップ（保持期間の半分を超えたログを削除）
    /// </summary>
    private async Task EmergencyLogCleanupAsync()
    {
        try
        {
            _logService.Warning("緊急ログクリーンアップを実行します");

            var archiveFolder = _settings.LogArchiveFolder;
            if (!Path.IsPathRooted(archiveFolder))
            {
                var logDir = Path.GetDirectoryName(_settings.LogFilePath) ?? ".";
                archiveFolder = Path.Combine(logDir, archiveFolder);
            }

            if (!Directory.Exists(archiveFolder))
            {
                return;
            }

            var archiveFiles = Directory.GetFiles(archiveFolder, "*.archive")
                .Select(f => new FileInfo(f))
                .Where(f => (DateTime.Now - f.LastWriteTime).TotalDays > _settings.LogRetentionDays / 2)
                .OrderBy(f => f.LastWriteTime)
                .ToList();

            int deletedCount = 0;
            foreach (var file in archiveFiles)
            {
                try
                {
                    File.Delete(file.FullName);
                    deletedCount++;
                }
                catch (Exception ex)
                {
                    _logService.Warning($"ファイル削除に失敗: {file.Name} - {ex.Message}");
                }
            }

            _logService.Information($"緊急ログクリーンアップが完了しました: {deletedCount}件削除");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "緊急ログクリーンアップ中にエラーが発生しました");
        }
    }
}

