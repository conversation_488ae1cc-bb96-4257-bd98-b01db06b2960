{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BatchManager.Executor/1.0.0": {"dependencies": {"BatchManager.Core": "1.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.9"}, "runtime": {"BatchManager.Executor.dll": {}}}, "BouncyCastle.Cryptography/2.6.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.6.1.59591"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "Dapper/2.1.66": {"runtime": {"lib/net8.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.66.48463"}}}, "MailKit/4.14.0": {"dependencies": {"MimeKit": "4.14.0", "System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "4.14.0.0", "fileVersion": "4.14.0.0"}}}, "Microsoft.Extensions.Configuration/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileSystemGlobbing": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Primitives/9.0.9": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "MimeKit/4.14.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "4.14.0.0", "fileVersion": "4.14.0.0"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "System.Configuration.ConfigurationManager/9.0.9": {"dependencies": {"System.Diagnostics.EventLog": "9.0.9", "System.Security.Cryptography.ProtectedData": "9.0.9"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Diagnostics.EventLog/9.0.9": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Formats.Asn1/8.0.1": {}, "System.IO.Pipelines/9.0.9": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/9.0.9": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Text.Encodings.Web/9.0.9": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Text.Json/9.0.9": {"dependencies": {"System.IO.Pipelines": "9.0.9", "System.Text.Encodings.Web": "9.0.9"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "BatchManager.Core/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Dapper": "2.1.66", "MailKit": "4.14.0", "Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Json": "9.0.9", "Microsoft.Extensions.DependencyInjection": "9.0.9", "Npgsql": "9.0.3", "Serilog": "4.3.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "7.0.0", "System.Configuration.ConfigurationManager": "9.0.9"}, "runtime": {"BatchManager.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BatchManager.Executor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BouncyCastle.Cryptography/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-vZsG2YILhthgRqO+ZVgRff4ZFKKTl0v7kqaVBLCtRvpREhfBP33pcWrdA3PRYgWuFL1RxiUFvjMUHTdBZlJcoA==", "path": "bouncycastle.cryptography/2.6.1", "hashPath": "bouncycastle.cryptography.2.6.1.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Dapper/2.1.66": {"type": "package", "serviceable": true, "sha512": "sha512-/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "path": "dapper/2.1.66", "hashPath": "dapper.2.1.66.nupkg.sha512"}, "MailKit/4.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-x1J8KIXGP8bWiiLox9g/hSdecqdDcOr9mZa4lumPjT1rvd+mnVm2pOOB4sYgABYcwW2uI7mAQMk7M+4OBD9iiA==", "path": "mailkit/4.14.0", "hashPath": "mailkit.4.14.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-w87wF/90/VI0ZQBhf4rbMEeyEy0vi2WKjFmACsNAKNaorY+ZlVz7ddyXkbADvaWouMKffNmR0yQOGcrvSSvKGg==", "path": "microsoft.extensions.configuration/9.0.9", "hashPath": "microsoft.extensions.configuration.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-p5RKAY9POvs3axwA/AQRuJeM8AHuE8h4qbP1NxQeGm0ep46aXz1oCLAp/oOYxX1GsjStgdhHrN3XXLLXr0+b3w==", "path": "microsoft.extensions.configuration.abstractions/9.0.9", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-AB8suTh4STAMGDkPer5vL0YNp09eplvbkIbOfFJ1z8D1zOiFF8Hipk9FhCLU4Ea6TosWmGrK30ZIUO9KvAeFcg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.9", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-fvgubCs++wTowHWuQ5TAyZV0S6ldA59U+tBVqFr4/WLd0oEf6ESbdBN2CFaVdn4sZqnarqMnl2O3++RG/Jrf/w==", "path": "microsoft.extensions.configuration.fileextensions/9.0.9", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-PiPYo1GTinR2ECM80zYdZUIFmde6jj5DryXUcOJg3yIjh+KQMQr42e+COD03QUsUiqNkJk511wVTnVpTm2AVZA==", "path": "microsoft.extensions.configuration.json/9.0.9", "hashPath": "microsoft.extensions.configuration.json.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zQV2WOSP+3z1EuK91ULxfGgo2Y75bTRnmJHp08+w/YXAyekZutX/qCd88/HOMNh35MDW9mJJJxPpMPS+1Rww8A==", "path": "microsoft.extensions.dependencyinjection/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-/hymojfWbE9AlDOa0mczR44m00Jj+T3+HZO0ZnVTI032fVycI0ZbNOVFP6kqZMcXiLSYXzR2ilcwaRi6dzeGyA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-M1ZhL9QkBQ/k6l/Wjgcli5zrV86HzytQ+gQiNtk9vs9Ge1fb17KKZil9T6jd15p2x/BGfXpup7Hg55CC0kkfig==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.9", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-sRrPtEwbK23OCFOQ36Xn6ofiB0/nl54/BOdR7lJ/Vwg3XlyvUdmyXvFUS1EU5ltn+sQtbcPuy1l0hsysO8++SQ==", "path": "microsoft.extensions.fileproviders.physical/9.0.9", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-iQAgORaVIlkhcpxFnVEfjqNWfQCwBEEH7x2IanTwGafA6Tb4xiBoDWySTxUo3MV2NUV/PmwS/8OhT/elPnJCnw==", "path": "microsoft.extensions.filesystemglobbing/9.0.9", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-z4pyMePOrl733ltTowbN565PxBw1oAr8IHmIXNDiDqd22nFpYltX9KhrNC/qBWAG1/Zx5MHX+cOYhWJQYCO/iw==", "path": "microsoft.extensions.primitives/9.0.9", "hashPath": "microsoft.extensions.primitives.9.0.9.nupkg.sha512"}, "MimeKit/4.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0LtsMC8DCTkc030C3UgVqbltOJmV5cz4AX8ASowz2ZA+lxopXSYtC1XXYmenxy606aWFLwi5Xy4cC/zyYjbjQ==", "path": "mimekit/4.14.0", "hashPath": "mimekit.4.14.0.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-Q1LknxnRmmsUXt/ElBp739Gexppy0HeDYxvExpJq09jAYhpTHRRRkZIwfNKfM4BGRlFzRDVdnerZawxoE8naMg==", "path": "system.configuration.configurationmanager/9.0.9", "hashPath": "system.configuration.configurationmanager.9.0.9.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-wpsUfnyv8E5K4WQaok6weewvAbQhcLwXFcHBm5U0gdEaBs85N//ssuYvRPFWwz2rO/9/DFP3A1sGMzUFBj8y3w==", "path": "system.diagnostics.eventlog/9.0.9", "hashPath": "system.diagnostics.eventlog.9.0.9.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-VySjpsCLprojvat550Flrm3NQB982CPuDzILajqjQihFmrQXZPdQyktIbcpVPJyaExFYtAfY1DpwMdWQuS0kbw==", "path": "system.io.pipelines/9.0.9", "hashPath": "system.io.pipelines.9.0.9.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-XN37933FTzEkqGJoOTunvnvzAv/4VO/9wQ0QwsGcrR5KyQpYT0z4Ssm+f+fpY9bea6srypFp3JjNPHHC26xzLw==", "path": "system.security.cryptography.protecteddata/9.0.9", "hashPath": "system.security.cryptography.protecteddata.9.0.9.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-bzYTmAcmfelUOCBxvbgsfSr2tq94ydA2gJZAxZRcuNa0LlmlVz8JNHst6RG1qsDujyVYT4vjv06y8sCLbvCXdg==", "path": "system.text.encodings.web/9.0.9", "hashPath": "system.text.encodings.web.9.0.9.nupkg.sha512"}, "System.Text.Json/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-NEnpppwq67fRz/OvQRxsEMgetDJsxlxpEsAFO/4PZYbAyAMd4Ol6KS7phc8uDoKPsnbdzRLKobpX303uQwCqdg==", "path": "system.text.json/9.0.9", "hashPath": "system.text.json.9.0.9.nupkg.sha512"}, "BatchManager.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}