using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;


namespace BatchManager.Core.Services;

/// <summary>
/// MailKitを使用したメール送信サービス
/// </summary>
public class EmailService : IEmailService
{
    private readonly AppSettings _settings;
    private readonly ILogService _logService;

    public EmailService(AppSettings settings, ILogService logService)
    {
        _settings = settings;
        _logService = logService;
    }

    /// <summary>
    /// メールを送信する
    /// </summary>
    public async Task SendEmailAsync(string subject, string body, bool isHtml = false)
    {
        // メール送信が無効の場合はスキップ
        if (!_settings.EmailEnabled)
        {
            _logService.Debug($"メール送信がスキップされました（EmailEnabled=false）: {subject}");
            return;
        }

        var recipients = ParseRecipients(_settings.EmailTo).ToList();
        if (recipients.Count == 0)
        {
            _logService.Warning($"メール送信がスキップされました（宛先未設定）: {subject}");
            return;
        }

        var host = _settings.SmtpHost?.Trim();
        if (string.IsNullOrWhiteSpace(host))
        {
            _logService.Warning($"メール送信がスキップされました（SmtpHost未設定）: {subject}");
            return;
        }

        var port = _settings.SmtpPort > 0 ? _settings.SmtpPort : 25;

        try
        {
            _logService.Debug($"メールを送信しています: {subject}");

            // メールメッセージの作成
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("BatchManagerNet", _settings.EmailFrom));
            
            // 複数の宛先をサポート（カンマ区切り）
            foreach (var address in recipients)
            {
                try
                {
                    message.To.Add(MailboxAddress.Parse(address));
                }
                catch (FormatException ex)
                {
                    _logService.Warning($"無効なメールアドレスをスキップしました: {address} ({ex.Message})");
                }
            }

            if (message.To.Count == 0)
            {
                _logService.Warning($"メール送信がスキップされました（有効な宛先なし）: {subject}");
                return;
            }

            // 件名にプレフィックスを追加
            message.Subject = $"{_settings.EmailSubjectPrefix} {subject}";

            // 本文の作成
            var bodyBuilder = new BodyBuilder();
            if (isHtml)
            {
                bodyBuilder.HtmlBody = body;
            }
            else
            {
                bodyBuilder.TextBody = body;
            }
            message.Body = bodyBuilder.ToMessageBody();

            // SMTP接続とメール送信
            using var client = new SmtpClient();
            
            // SSL設定
            var secureSocketOptions = _settings.SmtpEnableSsl 
                ? SecureSocketOptions.StartTlsWhenAvailable 
                : SecureSocketOptions.None;

            await ConnectWithFallbackAsync(client, host, port, secureSocketOptions);

            // 認証
            if (!string.IsNullOrEmpty(_settings.SmtpUsername) && !string.IsNullOrEmpty(_settings.SmtpPassword))
            {
                // ユーザー名のデコード（%が含まれている場合）
                var username = Uri.UnescapeDataString(_settings.SmtpUsername);
                await client.AuthenticateAsync(username, _settings.SmtpPassword);
            }

            // メール送信
            await client.SendAsync(message);
            await client.DisconnectAsync(true);

            _logService.Information($"メールを送信しました: {subject}");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, $"メール送信に失敗しました: {subject}");
            throw;
        }
    }

    private static readonly char[] RecipientSeparators = new[] { ',', ';', '\n', '\r' };

    private static IEnumerable<string> ParseRecipients(string? rawRecipients)
    {
        if (string.IsNullOrWhiteSpace(rawRecipients))
        {
            yield break;
        }

        foreach (var token in rawRecipients.Split(RecipientSeparators, StringSplitOptions.RemoveEmptyEntries))
        {
            var trimmed = token.Trim();
            if (!string.IsNullOrEmpty(trimmed))
            {
                yield return trimmed;
            }
        }
    }

    private async Task ConnectWithFallbackAsync(SmtpClient client, string host, int port, SecureSocketOptions options)
    {
        try
        {
            await client.ConnectAsync(host, port, options);
            return;
        }
        catch (SocketException ex) when (ex.SocketErrorCode == SocketError.HostNotFound || ex.SocketErrorCode == SocketError.NoData)
        {
            _logService.Warning($"SMTPホストへの接続に失敗したためIPアドレスで再試行します: {host} ({ex.SocketErrorCode})");

            IPAddress[] addresses;
            try
            {
                addresses = await Dns.GetHostAddressesAsync(host);
            }
            catch (Exception lookupEx)
            {
                _logService.Error(lookupEx, $"SMTPホスト名の解決に失敗しました: {host}");
                throw;
            }

            foreach (var address in addresses)
            {
                try
                {
                    await client.ConnectAsync(address.ToString(), port, options);
                    _logService.Debug($"SMTPサーバーへIPアドレスで接続しました: {address}");
                    return;
                }
                catch (SocketException retryEx)
                {
                    _logService.Warning($"SMTP接続の再試行に失敗しました: {address} ({retryEx.SocketErrorCode})");
                }
            }

            throw;
        }
    }

    /// <summary>
    /// タイムアウト通知メールを送信する
    /// </summary>
    public async Task SendTimeoutNotificationAsync(string jobName, int timeoutSeconds, string instanceName)
    {
        var subject = $"【タイムアウト通知】{instanceName} - {jobName}";
        var body = $@"バッチジョブがタイムアウトしました。

インスタンス名: {instanceName}
ジョブ名: {jobName}
タイムアウト設定: {timeoutSeconds}秒
発生日時: {DateTime.Now:yyyy/MM/dd HH:mm:ss}

詳細はログファイルを確認してください。

-- BatchManagerNet 自動通知 --";

        await SendEmailAsync(subject, body);
    }

    /// <summary>
    /// エラー通知メールを送信する
    /// </summary>
    public async Task SendErrorNotificationAsync(string jobName, string errorMessage, string instanceName)
    {
        var subject = $"【エラー通知】{instanceName} - {jobName}";
        var body = $@"バッチジョブでエラーが発生しました。

インスタンス名: {instanceName}
ジョブ名: {jobName}
エラー内容: {errorMessage}
発生日時: {DateTime.Now:yyyy/MM/dd HH:mm:ss}

詳細はログファイルを確認してください。

-- BatchManagerNet 自動通知 --";

        await SendEmailAsync(subject, body);
    }

    /// <summary>
    /// バッチ完了通知メールを送信する
    /// </summary>
    public async Task SendCompletionNotificationAsync(
        string instanceName, 
        int totalJobs, 
        int successJobs, 
        int timeoutJobs, 
        int errorJobs, 
        TimeSpan duration)
    {
        var failedJobs = timeoutJobs + errorJobs;
        var status = failedJobs == 0 ? "成功" : "警告";
        var subject = $"【バッチ完了通知】{instanceName} - {status}";
        
        var body = $@"バッチ処理が完了しました。

インスタンス名: {instanceName}
実行ジョブ数: {totalJobs}件
成功: {successJobs}件
タイムアウト: {timeoutJobs}件
エラー: {errorJobs}件
実行時間: {duration.TotalMinutes:F2}分
完了日時: {DateTime.Now:yyyy/MM/dd HH:mm:ss}

詳細はログファイルを確認してください。

-- BatchManagerNet 自動通知 --";

        await SendEmailAsync(subject, body);
    }
}
