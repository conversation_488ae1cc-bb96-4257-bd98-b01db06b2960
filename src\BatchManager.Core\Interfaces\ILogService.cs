namespace BatchManager.Core.Interfaces;

/// <summary>
/// ロギングを行うサービスのインターフェース
/// </summary>
public interface ILogService
{
    /// <summary>
    /// 詳細ログを記録
    /// </summary>
    void Verbose(string message);

    /// <summary>
    /// デバッグログを記録
    /// </summary>
    void Debug(string message);

    /// <summary>
    /// 情報ログを記録
    /// </summary>
    void Information(string message);

    /// <summary>
    /// 警告ログを記録
    /// </summary>
    void Warning(string message);

    /// <summary>
    /// エラーログを記録
    /// </summary>
    void Error(string message);

    /// <summary>
    /// エラーログを例外とともに記録
    /// </summary>
    void Error(Exception exception, string message);

    /// <summary>
    /// 致命的エラーログを記録
    /// </summary>
    void Fatal(string message);

    /// <summary>
    /// 致命的エラーログを例外とともに記録
    /// </summary>
    void Fatal(Exception exception, string message);
}



