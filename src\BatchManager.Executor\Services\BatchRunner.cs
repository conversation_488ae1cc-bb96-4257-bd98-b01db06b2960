using BatchManager.Core.Interfaces;
using BatchManager.Core.Models;

namespace BatchManager.Executor.Services;

/// <summary>
/// バッチ実行の制御を行うクラス
/// 4フェーズ実行フロー: 初期化 → ジョブ実行 → 完了処理 → DB転送
/// </summary>
public class BatchRunner
{
    private readonly IConfigService _configService;
    private readonly ILogService _logService;
    private readonly IDatabaseService? _databaseService;
    private readonly IEmailService _emailService;
    private readonly AppSettings _settings;
    private readonly JobExecutor _jobExecutor;
    private readonly ExecutionResultCollector _resultCollector;

    public BatchRunner(
        IConfigService configService,
        ILogService logService,
        IEmailService emailService,
        AppSettings settings,
        JobExecutor jobExecutor,
        ExecutionResultCollector resultCollector,
        IDatabaseService? databaseService = null)
    {
        _configService = configService;
        _logService = logService;
        _emailService = emailService;
        _settings = settings;
        _jobExecutor = jobExecutor;
        _resultCollector = resultCollector;
        _databaseService = databaseService;
    }

    /// <summary>
    /// バッチを実行する
    /// </summary>
    /// <returns>終了コード（0: 成功、1: エラー）</returns>
    public async Task<int> RunAsync()
    {
        var batchStartTime = DateTime.Now;
        var hasError = false;

        try
        {
            // === フェーズ1: 初期化 ===
            _logService.Information("===== フェーズ1: 初期化 =====");

            // 設定ファイルの読み込み
            var config = await _configService.LoadJobConfigAsync(_settings.ConfigFilePath);
            if (config == null || config.ComLists == null || config.ComLists.Length == 0)
            {
                _logService.Error("ジョブ設定が空です");
                return 1;
            }

            _logService.Information($"ジョブ数: {config.ComLists.Length}");

            // バッチ実行履歴の初期化
            _resultCollector.Initialize(
                _settings.InstanceName, 
                _settings.CustomerName, 
                _settings.BatchDescription, 
                batchStartTime, 
                config.ComLists.Length);

            // 前回失敗したDB転送データの再送信チェック
            if (_settings.DatabaseEnabled && _databaseService != null)
            {
                await RetryFailedUploadsAsync();
            }

            // === フェーズ2: ジョブ実行（メモリに蓄積） ===
            _logService.Information("===== フェーズ2: ジョブ実行 =====");

            for (int i = 0; i < config.ComLists.Length; i++)
            {
                var job = config.ComLists[i];
                var jobSequence = i + 1;

                // ジョブが無効の場合はスキップ
                if (!job.Enabled)
                {
                    _logService.Information($"[{jobSequence}/{config.ComLists.Length}] ジョブはスキップされました（無効）: {job.ComLine}");
                    continue;
                }

                _logService.Information($"[{jobSequence}/{config.ComLists.Length}] ジョブ実行開始: {job.ComLine}");

                // ジョブの実行
                var jobResult = await _jobExecutor.ExecuteJobAsync(job, jobSequence);

                // 結果をメモリに蓄積
                _resultCollector.AddJobResult(jobResult);

                // エラー処理
                if (jobResult.Status == "Failed")
                {
                    hasError = true;
                    _logService.Error($"ジョブ実行エラー: {jobResult.ErrorMessage}");

                    // エラー通知メール送信（endFlg が 4 以外）
                    if (job.EndFlg != 4)
                    {
                        try
                        {
                            await _emailService.SendErrorNotificationAsync(
                                jobResult.JobName,
                                jobResult.ErrorMessage ?? "不明なエラー",
                                _settings.InstanceName
                            );
                        }
                        catch (Exception mailEx)
                        {
                            _logService.Warning($"エラーメール送信に失敗しました: {mailEx.Message}");
                            // メール送信失敗してもバッチは続行
                        }
                    }

                    // endFlg に応じた処理
                    if (job.EndFlg == 2 || job.EndFlg == 3)
                    {
                        _logService.Warning($"endFlg={job.EndFlg} のため、後続ジョブをスキップします");
                        break;
                    }
                }
                else if (jobResult.Status == "Timeout")
                {
                    _logService.Warning($"ジョブがタイムアウトしました: {job.ComLine}");

                    // タイムアウト通知メール送信（endFlg が 4 以外）
                    if (job.EndFlg != 4)
                    {
                        try
                        {
                            await _emailService.SendTimeoutNotificationAsync(
                                jobResult.JobName,
                                job.LimitTime,
                                _settings.InstanceName
                            );
                        }
                        catch (Exception mailEx)
                        {
                            _logService.Warning($"タイムアウトメール送信に失敗しました: {mailEx.Message}");
                            // メール送信失敗してもバッチは続行
                        }
                    }

                    // endFlg に応じた処理
                    if (job.EndFlg == 2 || job.EndFlg == 3)
                    {
                        _logService.Warning($"endFlg={job.EndFlg} のため、後続ジョブをスキップします");
                        hasError = true;
                        break;
                    }
                }
                else
                {
                    _logService.Information($"[{jobSequence}/{config.ComLists.Length}] ジョブ実行完了: 成功");
                }
            }

            // === フェーズ3: 完了処理 ===
            _logService.Information("===== フェーズ3: 完了処理 =====");

            var batchEndTime = DateTime.Now;
            var batchResult = _resultCollector.Finalize(batchEndTime, hasError);

            _logService.Information($"バッチ実行完了:");
            _logService.Information($"  総ジョブ数: {batchResult.TotalJobs}");
            _logService.Information($"  成功: {batchResult.SuccessfulJobs}");
            _logService.Information($"  タイムアウト: {batchResult.TimeoutJobs}");
            _logService.Information($"  失敗: {batchResult.FailedJobs}");
            _logService.Information($"  実行時間: {batchResult.TotalDurationSeconds:F2}秒");

            // === フェーズ4: DB転送（非同期・別スレッド） ===
            if (_settings.DatabaseEnabled && _databaseService != null)
            {
                _logService.Information("===== フェーズ4: DB転送 =====");

                if (_settings.DbUploadAsync)
                {
                    // 非同期転送（別スレッドで実行、バッチの終了を待たない）
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await UploadToDatabaseAsync(batchResult);
                        }
                        catch (Exception ex)
                        {
                            _logService.Error(ex, "DB転送中にエラーが発生しました（非同期）");
                        }
                    });
                    _logService.Information("DB転送を非同期で開始しました");
                }
                else
                {
                    // 同期転送（完了を待つ）
                    await UploadToDatabaseAsync(batchResult);
                }
            }

            // バッチの終了コードを返す（DB転送の結果には影響されない）
            return hasError ? 1 : 0;
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "バッチ実行中に致命的なエラーが発生しました");
            return 1;
        }
    }

    /// <summary>
    /// データベースにバッチ実行結果をアップロードする
    /// </summary>
    private async Task UploadToDatabaseAsync(BatchExecution batchResult)
    {
        if (_databaseService == null)
        {
            _logService.Warning("データベースサービスが利用できません");
            return;
        }

        try
        {
            _logService.Information("データベースへのアップロードを開始します...");

            // バッチ実行履歴の挿入
            var batchId = await _databaseService.InsertBatchExecutionAsync(batchResult);
            _logService.Information($"バッチ実行履歴を登録しました (ID: {batchId})");

            // ジョブ実行履歴の一括挿入
            var jobResults = _resultCollector.GetJobResults();
            foreach (var job in jobResults)
            {
                job.BatchExecutionId = batchId;
            }

            await _databaseService.InsertJobExecutionsBatchAsync(jobResults);
            _logService.Information($"ジョブ実行履歴を登録しました ({jobResults.Count}件)");

            _logService.Information("データベースへのアップロード完了");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "データベースへのアップロードに失敗しました");

            // 失敗時の処理
            if (_settings.DbUploadFailAction == "SaveAndContinue")
            {
                await SaveFailedUploadAsync(batchResult);
            }
        }
    }

    /// <summary>
    /// アップロード失敗時に一時ファイルとして保存
    /// </summary>
    private async Task SaveFailedUploadAsync(BatchExecution batchResult)
    {
        try
        {
            var tempFolder = _settings.DbUploadTempFolder;
            if (!Directory.Exists(tempFolder))
            {
                Directory.CreateDirectory(tempFolder);
            }

            var fileName = $"batch_{batchResult.StartTime:yyyyMMddHHmmss}_{Guid.NewGuid()}.json";
            var filePath = Path.Combine(tempFolder, fileName);

            var data = new
            {
                BatchExecution = batchResult,
                JobExecutions = _resultCollector.GetJobResults()
            };

            var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(filePath, json);
            _logService.Information($"アップロード失敗データを保存しました: {filePath}");
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "失敗データの保存に失敗しました");
        }
    }

    /// <summary>
    /// 前回失敗したアップロードの再送信を試みる
    /// </summary>
    private async Task RetryFailedUploadsAsync()
    {
        try
        {
            var tempFolder = _settings.DbUploadTempFolder;
            if (!Directory.Exists(tempFolder))
            {
                return;
            }

            var files = Directory.GetFiles(tempFolder, "batch_*.json");
            if (files.Length == 0)
            {
                return;
            }

            _logService.Information($"前回失敗したアップロードを再送信します ({files.Length}件)");

            foreach (var file in files)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var data = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);

                    if (data != null && _databaseService != null)
                    {
                        // 再アップロード処理
                        _logService.Information($"再送信中: {Path.GetFileName(file)}");

                        // 成功したらファイルを削除
                        File.Delete(file);
                        _logService.Information($"再送信成功: {Path.GetFileName(file)}");
                    }
                }
                catch (Exception ex)
                {
                    _logService.Warning($"ファイルの再送信に失敗しました: {Path.GetFileName(file)} - {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            _logService.Error(ex, "失敗データの再送信中にエラーが発生しました");
        }
    }
}

