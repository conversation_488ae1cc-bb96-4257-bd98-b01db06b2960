﻿<Window x:Class="BatchManager.ConfigEditor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="BatchConfigEditor - BatchManagerNet設定エディター" 
        Height="600" Width="1000"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>
    
    <DockPanel>
        <!-- メニューバー -->
        <Menu DockPanel.Dock="Top">
            <MenuItem Header="ファイル (_F)">
                <MenuItem Header="開く (_O)" Command="{Binding OpenConfigFileCommand}"/>
                <MenuItem Header="保存 (_S)" Command="{Binding SaveConfigFileCommand}"/>
                <MenuItem Header="名前を付けて保存 (_A)" Command="{Binding SaveConfigFileAsCommand}"/>
                <Separator/>
                <MenuItem Header="終了 (_X)" Click="MenuItem_Exit_Click"/>
            </MenuItem>
        </Menu>
        
        <!-- ステータスバー -->
        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
        </StatusBar>
        
        <!-- メインコンテンツ -->
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- ツールバー -->
            <ToolBarTray Grid.Row="0">
                <ToolBar>
                    <Button Content="開く" Command="{Binding OpenConfigFileCommand}"/>
                    <Button Content="保存" Command="{Binding SaveConfigFileCommand}"/>
                    <Separator/>
                    <Button Content="追加" Command="{Binding AddJobCommand}"/>
                    <Button Content="削除" Command="{Binding DeleteJobCommand}"/>
                    <Separator/>
                    <Button Content="↑" Command="{Binding MoveJobUpCommand}" ToolTip="上に移動"/>
                    <Button Content="↓" Command="{Binding MoveJobDownCommand}" ToolTip="下に移動"/>
                </ToolBar>
            </ToolBarTray>
            
            <!-- ジョブ一覧 DataGrid -->
            <DataGrid Grid.Row="1" 
                      ItemsSource="{Binding Jobs}" 
                      SelectedItem="{Binding SelectedJob}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      Margin="0,10,0,0">
                <DataGrid.Columns>
                    <DataGridCheckBoxColumn Header="有効" Width="50" Binding="{Binding Enabled}"/>
                    <DataGridTextColumn Header="説明" Width="150" Binding="{Binding Description}"/>
                    <DataGridTextColumn Header="コマンドライン" Width="*" Binding="{Binding ComLine}"/>
                    <DataGridTextColumn Header="パラメータ" Width="150" Binding="{Binding Param}"/>
                    <DataGridTextColumn Header="タイプ" Width="60" Binding="{Binding Type}"/>
                    <DataGridTextColumn Header="タイムアウト(秒)" Width="100" Binding="{Binding LimitTime}"/>
                    <DataGridTextColumn Header="終了フラグ" Width="80" Binding="{Binding EndFlg}"/>
                </DataGrid.Columns>
            </DataGrid>
            
            <!-- ジョブ詳細編集エリア -->
            <GroupBox Grid.Row="2" Header="選択されたジョブ" Margin="0,10,0,0" Padding="10">
                <Grid DataContext="{Binding SelectedJob}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 説明 -->
                    <Label Grid.Row="0" Grid.Column="0" Content="説明:"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}" Margin="5"/>
                    
                    <CheckBox Grid.Row="0" Grid.Column="2" Content="有効" IsChecked="{Binding Enabled}" Margin="10,5"/>
                    <CheckBox Grid.Row="0" Grid.Column="3" Content="成功時に通知" IsChecked="{Binding NotifyOnSuccess}" Margin="5"/>
                    
                    <!-- コマンドライン -->
                    <Label Grid.Row="1" Grid.Column="0" Content="コマンドライン:"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding ComLine, UpdateSourceTrigger=PropertyChanged}" Margin="5"/>
                    
                    <!-- パラメータ -->
                    <Label Grid.Row="2" Grid.Column="0" Content="パラメータ:"/>
                    <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding Param, UpdateSourceTrigger=PropertyChanged}" Margin="5"/>
                    
                    <!-- その他設定 -->
                    <Label Grid.Row="3" Grid.Column="0" Content="タイプ:"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Type}" Margin="5" Width="200" HorizontalAlignment="Left" ToolTip="1=バッチファイル, 2=EXEファイル"/>
                    
                    <Label Grid.Row="3" Grid.Column="2" Content="タイムアウト(秒):"/>
                    <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding LimitTime}" Margin="5"/>
                </Grid>
            </GroupBox>
        </Grid>
    </DockPanel>
</Window>
