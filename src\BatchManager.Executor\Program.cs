using BatchManager.Core.Interfaces;
using BatchManager.Core.Services;
using BatchManager.Executor.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BatchManager.Executor;

/// <summary>
/// BatchManagerNet.exe のエントリーポイント
/// </summary>
class Program
{
    static async Task<int> Main(string[] args)
    {
        try
        {
            // コマンドライン引数の解析
            var commandLineArgs = ParseCommandLineArgs(args);

            // サービスコンテナの構築
            var services = new ServiceCollection();
            ConfigureServices(services, commandLineArgs);

            var serviceProvider = services.BuildServiceProvider();

            // ログサービスの取得
            var logService = serviceProvider.GetRequiredService<ILogService>();
            logService.Information("===== BatchManagerNet 起動 =====");

            // コマンドライン引数の処理
            var configService = serviceProvider.GetRequiredService<IConfigService>();
            var settings = configService.LoadAppSettings();

            // /rotate-logs オプションの場合はログローテーションのみ実行
            if (commandLineArgs.ContainsKey("rotate-logs"))
            {
                logService.Information("ログローテーションモード");
                var rotationService = new LogRotationService(settings, logService);
                if (rotationService.ShouldRotate())
                {
                    logService.Information("ログローテーションを実行します");
                    await rotationService.RotateLogsAsync();
                    logService.Information("ログローテーション完了");
                }
                else
                {
                    logService.Information("ログローテーションは不要です");
                }
                return 0;
            }

            // 設定の検証
            var validationErrors = settings.Validate();
            if (validationErrors.Count > 0)
            {
                logService.Error("設定ファイルにエラーがあります:");
                foreach (var error in validationErrors)
                {
                    logService.Error($"  - {error}");
                }
                return 1;
            }

            logService.Information($"インスタンス名: {settings.InstanceName}");
            logService.Information($"設定ファイル: {settings.ConfigFilePath}");

            // ログローテーションチェック
            var logRotationService = new LogRotationService(settings, logService);
            if (logRotationService.ShouldRotate())
            {
                logService.Information("ログローテーションを実行します");
                await logRotationService.RotateLogsAsync();
            }

            // バッチ実行
            var batchRunner = serviceProvider.GetRequiredService<BatchRunner>();
            var exitCode = await batchRunner.RunAsync();

            logService.Information($"===== BatchManagerNet 終了 (ExitCode: {exitCode}) =====");
            return exitCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"致命的なエラーが発生しました: {ex.Message}");
            Console.Error.WriteLine(ex.StackTrace);
            return 1;
        }
    }

    /// <summary>
    /// コマンドライン引数を解析する
    /// </summary>
    private static Dictionary<string, string> ParseCommandLineArgs(string[] args)
    {
        var result = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        
        foreach (var arg in args)
        {
            if (arg.StartsWith("/") || arg.StartsWith("-"))
            {
                var parts = arg.Substring(1).Split('=', 2);
                var key = parts[0].ToLower();
                var value = parts.Length > 1 ? parts[1].Trim('"') : "true";
                result[key] = value;
            }
        }
        
        return result;
    }

    /// <summary>
    /// 依存性注入の設定
    /// </summary>
    private static void ConfigureServices(IServiceCollection services, Dictionary<string, string> commandLineArgs)
    {
        // 設定サービスの登録（最初に必要）
        var environment = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production";
        var configuration = new ConfigurationBuilder()
            .SetBasePath(AppContext.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environment}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        var tempConfigService = new ConfigService(new LogService("temp.log"), configuration);
        var settings = tempConfigService.LoadAppSettings();

        // コマンドライン引数で設定を上書き
        if (commandLineArgs.ContainsKey("config"))
        {
            settings.ConfigFilePath = commandLineArgs["config"];
        }

        // ログサービスの登録
        var logService = new LogService(
            settings.LogFilePath,
            settings.LogLevel,
            settings.LogFormat
        );
        services.AddSingleton<ILogService>(logService);

        // 設定サービスの登録
        services.AddSingleton(configuration);
        services.AddSingleton<IConfigService>(new ConfigService(logService, configuration));

        // データベースサービスの登録
        if (settings.DatabaseEnabled)
        {
            services.AddSingleton<IDatabaseService>(
                new DatabaseService(settings.DbConnectionString, logService)
            );
        }

        // メールサービスの登録
        services.AddSingleton<IEmailService>(new EmailService(settings, logService));

        // アプリケーション設定の登録
        services.AddSingleton(settings);

        // バッチ実行サービスの登録
        services.AddSingleton<BatchRunner>();
        services.AddSingleton<JobExecutor>();
        services.AddSingleton<ExecutionResultCollector>();
    }
}
