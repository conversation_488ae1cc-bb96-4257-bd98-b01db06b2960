using BatchManager.Core.Models;

namespace BatchManager.Core.Interfaces;

/// <summary>
/// データベース操作を行うサービスのインターフェース
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// データベース接続をテストする
    /// </summary>
    /// <returns>接続成功の可否</returns>
    Task<bool> TestConnectionAsync();

    /// <summary>
    /// バッチ実行履歴を挿入する
    /// </summary>
    /// <param name="batchExecution">バッチ実行履歴</param>
    /// <returns>挿入されたレコードのID</returns>
    Task<long> InsertBatchExecutionAsync(BatchExecution batchExecution);

    /// <summary>
    /// バッチ実行履歴を更新する
    /// </summary>
    /// <param name="batchExecution">更新するバッチ実行履歴</param>
    Task UpdateBatchExecutionAsync(BatchExecution batchExecution);

    /// <summary>
    /// ジョブ実行履歴を挿入する
    /// </summary>
    /// <param name="jobExecution">ジョブ実行履歴</param>
    /// <returns>挿入されたレコードのID</returns>
    Task<long> InsertJobExecutionAsync(JobExecution jobExecution);

    /// <summary>
    /// ジョブ実行履歴を一括挿入する
    /// </summary>
    /// <param name="jobExecutions">ジョブ実行履歴のリスト</param>
    Task InsertJobExecutionsBatchAsync(IEnumerable<JobExecution> jobExecutions);

    /// <summary>
    /// バッチ実行履歴を取得する
    /// </summary>
    /// <param name="id">バッチ実行ID</param>
    /// <returns>バッチ実行履歴</returns>
    Task<BatchExecution?> GetBatchExecutionAsync(long id);

    /// <summary>
    /// 指定期間のバッチ実行履歴を取得する
    /// </summary>
    /// <param name="startDate">開始日時</param>
    /// <param name="endDate">終了日時</param>
    /// <param name="instanceName">インスタンス名（省略可）</param>
    /// <returns>バッチ実行履歴のリスト</returns>
    Task<List<BatchExecution>> GetBatchExecutionsByPeriodAsync(
        DateTime startDate,
        DateTime endDate,
        string? instanceName = null);

    /// <summary>
    /// ジョブ統計情報を取得する
    /// </summary>
    /// <param name="instanceName">インスタンス名（省略可）</param>
    /// <param name="days">過去何日分のデータを取得するか（デフォルト: 30日）</param>
    /// <returns>ジョブ統計情報のリスト</returns>
    Task<List<JobStatistics>> GetJobStatisticsAsync(string? instanceName = null, int days = 30);
}

/// <summary>
/// ジョブ統計情報
/// </summary>
public class JobStatistics
{
    public string JobName { get; set; } = string.Empty;
    public int TotalExecutions { get; set; }
    public int SuccessCount { get; set; }
    public int TimeoutCount { get; set; }
    public int ErrorCount { get; set; }
    public double SuccessRate { get; set; }
    public double AverageExecutionSeconds { get; set; }
    public double MaxExecutionSeconds { get; set; }
    public double MinExecutionSeconds { get; set; }
}



