{"@t":"2025-10-01T03:57:11.9136948Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T03:57:11.9708891Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T03:57:12.0550639Z","@mt":"アプリケーション設定を読み込みました: DefaultInstance"}
{"@t":"2025-10-01T07:44:45.1983192Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T07:44:45.2412342Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T07:44:45.2971719Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:02:12.5589448Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:02:12.6019264Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:02:12.6507436Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:10:47.4177867Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:10:47.4552101Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:10:47.5002465Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:16:09.3276934Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:16:09.3589013Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:16:09.4135546Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:19:43.1016349Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:19:43.1357813Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:19:43.1838628Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:21:12.0698922Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:21:12.1161705Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:21:12.1622158Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:22:05.3683761Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:22:05.4108638Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:22:05.4672081Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:25:53.1458471Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:25:53.1736533Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:25:53.2236467Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:28:07.6298840Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:28:07.6652627Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:28:07.7109286Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:31:37.9885120Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:31:38.0257569Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:31:38.0701121Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:32:55.6768240Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:32:55.7208720Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:32:55.7860707Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:33:37.9857213Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:33:38.0168836Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:33:38.0604923Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:33:44.2805920Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:33:44.3202274Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:33:44.3702389Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:37:52.4212191Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:37:52.4677989Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:37:52.5315651Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:40:08.3914103Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:40:08.4194899Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:40:08.4714001Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:41:02.4647974Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:41:02.4982118Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:41:02.5500252Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:44:03.6838745Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:44:03.7198803Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:44:03.7771150Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:46:07.0919408Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T08:46:07.1253222Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T08:46:07.1779412Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T08:46:07.1783190Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:01:43.3616842Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:01:43.4037603Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:01:43.4582055Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:01:43.4584903Z","@mt":"メール設定: EmailEnabled=False, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:04:29.2448548Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:04:29.2775920Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:04:29.3249475Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:04:29.3251548Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:41:33.6530758Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:41:33.6798807Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:41:33.6815948Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:41:33.6817200Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:41:33.6817763Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:41:33.6818178Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:41:33.6818658Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:41:33.6820777Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.6821410Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:41:33.6821793Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:41:33.6822129Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:41:33.6823116Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:41:33.6827994Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.6828745Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:41:33.6829199Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:41:33.6829615Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:41:33.7286279Z","@mt":"設定値解決: FromMailAddress Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7288945Z","@mt":"設定値解決: ToMailAddress Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7289653Z","@mt":"設定値解決: SmtpClient Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7290061Z","@mt":"設定値解決: User Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7290495Z","@mt":"設定値解決: Pass Json='<null>' AppConfig='<null>' Result='<null>'"}
{"@t":"2025-10-01T09:41:33.7290860Z","@mt":"設定値解決: EnableSsl Json='<null>' AppConfig='<null>' Result='true'"}
{"@t":"2025-10-01T09:41:33.7291238Z","@mt":"設定値解決: Port Json='<null>' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:41:33.7291696Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:41:33.7292148Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:41:33.7292595Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:41:33.7296928Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:41:33.7319614Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:41:33.7320599Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:41:33.7321207Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:41:33.7321794Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:41:33.7322167Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:41:33.7322520Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:41:33.7323064Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:41:33.7323424Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:41:33.7323735Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:41:33.7324897Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:41:33.7325349Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:41:33.7325751Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:41:33.7326242Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:41:33.7327022Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='', EmailFrom='', EmailTo=''"}
{"@t":"2025-10-01T09:43:07.7595766Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:43:07.7952560Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:43:07.7986516Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:43:07.7988846Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:43:07.7990548Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:43:07.7991518Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:43:07.7992069Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:43:07.7993809Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.7994784Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:43:07.7995512Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:43:07.7996103Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:43:07.7997625Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:43:07.8001905Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.8002788Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:43:07.8003389Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:43:07.8004017Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:43:07.8717878Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:43:07.8722429Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:43:07.8723772Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:43:07.8729509Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:43:07.8757590Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:43:07.8758701Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:43:07.8759518Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:43:07.8760999Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:43:07.8761684Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:43:07.8762268Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:43:07.8762755Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:43:07.8763258Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:43:07.8763713Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:43:07.8764143Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:43:07.8764679Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:43:07.8765133Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:43:07.8770805Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:43:07.8771849Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.4906221Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:44:01.6157856Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:44:01.6182649Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:44:01.6187357Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:44:01.6192379Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:44:01.6196511Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:44:01.6202272Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:44:01.6208898Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.6214630Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.6220363Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:44:01.6225880Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:44:01.6231141Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:44:01.6240920Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.6246221Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:44:01.6250608Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:44:01.6254790Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:44:01.6650590Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:44:01.6661763Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:44:01.6668525Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:44:01.6680619Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:44:01.6713373Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:44:01.6720059Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:44:01.6725651Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:44:01.6732608Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:44:01.6738045Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:44:01.6743768Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:44:01.6748912Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:44:01.6754379Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:44:01.6759262Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:44:01.6764566Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:44:01.6769521Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:44:01.6774333Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:44:01.6782980Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:44:01.6788698Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.7833457Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:45:25.8210481Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:45:25.8232019Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:45:25.8236898Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:45:25.8240806Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:45:25.8244418Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:45:25.8247644Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:45:25.8251758Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.8255037Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.8258460Z","@mt":"設定値解決: EmailTo Json='<EMAIL>;<EMAIL>' AppConfig='<null>' Result='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:45:25.8262472Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:45:25.8266528Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:45:25.8272498Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.8276298Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:45:25.8279757Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:45:25.8282934Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:45:25.8633476Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:45:25.8694344Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:45:25.8699775Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:45:25.8713853Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:45:25.8741528Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:45:25.8746426Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:45:25.8750568Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:45:25.8755233Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:45:25.8759455Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:45:25.8763652Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:45:25.8767695Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:45:25.8771766Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:45:25.8775621Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:45:25.8779430Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:45:25.8783266Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:45:25.8789627Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:45:25.8796243Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:45:25.8800815Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>;<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.3733448Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:46:55.4103143Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:46:55.4139984Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:46:55.4142549Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:46:55.4145029Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:46:55.4146383Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:46:55.4147485Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:46:55.4150370Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4152686Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.4154573Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:46:55.4156275Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:46:55.4160043Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:46:55.4166158Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4167716Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:46:55.4169694Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:46:55.4171543Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:46:55.4776675Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:46:55.4781011Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:46:55.4782140Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:46:55.4788387Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:46:55.4823125Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:46:55.4824541Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:46:55.4825250Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:46:55.4825958Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:46:55.4826490Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:46:55.4827040Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:46:55.4827496Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:46:55.4828026Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:46:55.4828580Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:46:55.4829015Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:46:55.4830104Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:46:55.4830737Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:46:55.4834163Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:46:55.4834887Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.1973945Z","@mt":"ログサービスを初期化しました: temp.log"}
{"@t":"2025-10-01T09:57:14.2284386Z","@mt":"アプリケーション設定を読み込んでいます"}
{"@t":"2025-10-01T09:57:14.2723888Z","@mt":"設定値解決: InstanceName Json='BatchManagerNet' AppConfig='<null>' Result='BatchManagerNet'"}
{"@t":"2025-10-01T09:57:14.2725915Z","@mt":"設定値解決: CustomerName Json='サンプル顧客株式会社' AppConfig='<null>' Result='サンプル顧客株式会社'"}
{"@t":"2025-10-01T09:57:14.2726565Z","@mt":"設定値解決: BatchDescription Json='バッチ処理システム - テスト環境' AppConfig='<null>' Result='バッチ処理システム - テスト環境'"}
{"@t":"2025-10-01T09:57:14.2727285Z","@mt":"設定値解決: ConfigFilePath Json='BatchManagerNet.config' AppConfig='<null>' Result='BatchManagerNet.config'"}
{"@t":"2025-10-01T09:57:14.2727855Z","@mt":"設定値解決: LogFilePath Json='BatchManagerNet.log' AppConfig='<null>' Result='BatchManagerNet.log'"}
{"@t":"2025-10-01T09:57:14.2729105Z","@mt":"設定値解決: EmailEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.2729722Z","@mt":"設定値解決: EmailFrom Json='<EMAIL>' AppConfig='<null>' Result='<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.2730198Z","@mt":"設定値解決: EmailTo Json='<EMAIL>,<EMAIL>' AppConfig='<null>' Result='<EMAIL>,<EMAIL>'"}
{"@t":"2025-10-01T09:57:14.2730568Z","@mt":"設定値解決: SmtpHost Json='smtp.alpha-prm.jp' AppConfig='<null>' Result='smtp.alpha-prm.jp'"}
{"@t":"2025-10-01T09:57:14.2731593Z","@mt":"設定値解決: SmtpPort Json='587' AppConfig='<null>' Result='587'"}
{"@t":"2025-10-01T09:57:14.2734806Z","@mt":"設定値解決: SmtpEnableSsl Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.2735433Z","@mt":"設定値解決: SmtpUsername Json='noreply%rtsc.co.jp' AppConfig='<null>' Result='noreply%rtsc.co.jp'"}
{"@t":"2025-10-01T09:57:14.2735807Z","@mt":"設定値解決: SmtpPassword Json='***' AppConfig='<null>' Result='***'"}
{"@t":"2025-10-01T09:57:14.2736147Z","@mt":"設定値解決: EmailSubjectPrefix Json='[BatchManagerNet]' AppConfig='<null>' Result='[BatchManagerNet]'"}
{"@t":"2025-10-01T09:57:14.3267875Z","@mt":"設定値解決: LogRotationEnabled Json='<null>' AppConfig='true' Result='true'"}
{"@t":"2025-10-01T09:57:14.3270251Z","@mt":"設定値解決: LogRetentionDays Json='<null>' AppConfig='30' Result='30'"}
{"@t":"2025-10-01T09:57:14.3273467Z","@mt":"設定値解決: LogArchiveFolder Json='<null>' AppConfig='Archive' Result='Archive'"}
{"@t":"2025-10-01T09:57:14.3278534Z","@mt":"設定値解決: LogRotationTime Json='<null>' AppConfig='00:00' Result='00:00'"}
{"@t":"2025-10-01T09:57:14.3301746Z","@mt":"設定値解決: DatabaseEnabled Json='True' AppConfig='<null>' Result='True'"}
{"@t":"2025-10-01T09:57:14.3302762Z","@mt":"設定値解決: DatabaseType Json='<null>' AppConfig='<null>' Result='PostgreSQL'"}
{"@t":"2025-10-01T09:57:14.3303398Z","@mt":"設定値解決: DbConnectionString Json='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true' AppConfig='<null>' Result='Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true'"}
{"@t":"2025-10-01T09:57:14.3312346Z","@mt":"設定値解決: DbUploadMode Json='<null>' AppConfig='AfterCompletion' Result='AfterCompletion'"}
{"@t":"2025-10-01T09:57:14.3315476Z","@mt":"設定値解決: DbUploadAsync Json='False' AppConfig='<null>' Result='False'"}
{"@t":"2025-10-01T09:57:14.3316604Z","@mt":"設定値解決: DbUploadFailAction Json='<null>' AppConfig='SaveAndContinue' Result='SaveAndContinue'"}
{"@t":"2025-10-01T09:57:14.3317471Z","@mt":"設定値解決: DbUploadRetryCount Json='3' AppConfig='<null>' Result='3'"}
{"@t":"2025-10-01T09:57:14.3318036Z","@mt":"設定値解決: DbUploadRetryInterval Json='5' AppConfig='<null>' Result='5'"}
{"@t":"2025-10-01T09:57:14.3318482Z","@mt":"設定値解決: DbUploadTempFolder Json='UploadQueue' AppConfig='<null>' Result='UploadQueue'"}
{"@t":"2025-10-01T09:57:14.3318919Z","@mt":"設定値解決: DbUploadTimeout Json='30' AppConfig='<null>' Result='30'"}
{"@t":"2025-10-01T09:57:14.3319348Z","@mt":"設定値解決: LogFormat Json='JSON' AppConfig='<null>' Result='JSON'"}
{"@t":"2025-10-01T09:57:14.3319704Z","@mt":"設定値解決: LogLevel Json='Information' AppConfig='<null>' Result='Information'"}
{"@t":"2025-10-01T09:57:14.3322994Z","@mt":"アプリケーション設定を読み込みました: BatchManagerNet"}
{"@t":"2025-10-01T09:57:14.3324251Z","@mt":"メール設定: EmailEnabled=True, SmtpHost='smtp.alpha-prm.jp', EmailFrom='<EMAIL>', EmailTo='<EMAIL>,<EMAIL>'"}
