using BatchManager.Core.Interfaces;
using Serilog;
using Serilog.Events;

namespace BatchManager.Core.Services;

/// <summary>
/// Serilogを使用したロギングサービス
/// </summary>
public class LogService : ILogService
{
    private readonly ILogger _logger;

    public LogService(string logFilePath, string logLevel = "Information", string logFormat = "JSON")
    {
        // Serilogの設定
        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Is(ParseLogLevel(logLevel));

        // ログフォーマットに応じた設定
        if (logFormat.Equals("JSON", StringComparison.OrdinalIgnoreCase))
        {
            loggerConfig.WriteTo.File(
                new Serilog.Formatting.Compact.CompactJsonFormatter(),
                logFilePath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 31,
                shared: true,
                flushToDiskInterval: TimeSpan.FromSeconds(1)
            );
        }
        else
        {
            loggerConfig.WriteTo.File(
                logFilePath,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 31,
                shared: true,
                flushToDiskInterval: TimeSpan.FromSeconds(1)
            );
        }

        // コンソール出力も追加
        loggerConfig.WriteTo.Console(
            outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        );

        _logger = loggerConfig.CreateLogger();

        Information($"ログサービスを初期化しました: {logFilePath}");
    }

    public void Verbose(string message)
    {
        _logger.Verbose(message);
    }

    public void Debug(string message)
    {
        _logger.Debug(message);
    }

    public void Information(string message)
    {
        _logger.Information(message);
    }

    public void Warning(string message)
    {
        _logger.Warning(message);
    }

    public void Error(string message)
    {
        _logger.Error(message);
    }

    public void Error(Exception exception, string message)
    {
        _logger.Error(exception, message);
    }

    public void Fatal(string message)
    {
        _logger.Fatal(message);
    }

    public void Fatal(Exception exception, string message)
    {
        _logger.Fatal(exception, message);
    }

    public void Dispose()
    {
        Log.CloseAndFlush();
    }

    /// <summary>
    /// ログレベル文字列をSerilogのLogEventLevelに変換
    /// </summary>
    private LogEventLevel ParseLogLevel(string logLevel)
    {
        return logLevel.ToLower() switch
        {
            "verbose" => LogEventLevel.Verbose,
            "debug" => LogEventLevel.Debug,
            "information" => LogEventLevel.Information,
            "warning" => LogEventLevel.Warning,
            "error" => LogEventLevel.Error,
            "fatal" => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }
}



