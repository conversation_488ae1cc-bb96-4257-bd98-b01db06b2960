{"AppSettings": {"InstanceName": "BatchManagerNet", "CustomerName": "サンプル顧客株式会社", "BatchDescription": "バッチ処理システム - テスト環境", "ConfigFilePath": "BatchManagerNet.config", "LogFilePath": "BatchManagerNet.log", "EmailEnabled": true, "SmtpHost": "smtp.alpha-prm.jp", "SmtpPort": 587, "SmtpEnableSsl": true, "SmtpUsername": "noreply%rtsc.co.jp", "SmtpPassword": "al_noreply001", "EmailFrom": "<EMAIL>", "EmailTo": "<EMAIL>,<EMAIL>", "EmailSubjectPrefix": "[BatchManagerNet]", "DatabaseEnabled": true, "DbConnectionString": "Host=ep-sweet-bread-a1375kx4-pooler.ap-southeast-1.aws.neon.tech;Port=5432;Database=neondb;Username=neondb_owner;Password=****************;SSL Mode=Require;Trust Server Certificate=true", "DbUploadAsync": false, "DbUploadRetryCount": 3, "DbUploadRetryInterval": 5, "DbUploadTempFolder": "UploadQueue", "DbUploadTimeout": 30, "LogFormat": "JSON", "LogLevel": "Information"}}